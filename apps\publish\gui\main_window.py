"""
Main application window for the publish application.

This module contains the MainWindow class that handles the primary user interface
and coordinates with the service layer for business operations.
"""

import customtkinter as ctk
from tkinter import filedialog
from typing import Optional, Dict, Any
import logging

from ..models.project_data import ProjectData, TitleBlockData
from ..services.publish_service import PublishService, PublishConfig
from ..services.model_service import ModelService
from ..integrations.e3_client import E3Client
from .widgets import ModelDropdown, SeriesControls, E3InstanceSelector
from .dialogs import ErrorDialog, ConfirmationDialog


class MainWindow:
    """Main application window for project publishing."""
    
    def __init__(self, root: ctk.CTk, container):
        """
        Initialize the main window.
        
        Args:
            root: The root CTk window
            container: Service container for dependency injection
        """
        self.root = root
        self.container = container
        self.logger = logging.getLogger(__name__)
        
        # Get services from container
        try:
            self.publish_service = self.container.get_service(PublishService)
            self.model_service = self.container.get_service(ModelService)
            self.e3_client = self.container.get_service(E3Client)
        except Exception as e:
            self.logger.error(f"Failed to get services from container: {e}")
            # Set fallback None values
            self.publish_service = None
            self.model_service = None
            self.e3_client = None
        
        # Form fields - must be defined before setup_ui()
        self.fields = [
            "GSS Parent #",
            "Serial number", 
            "Customer",
            "Location",
            "Title",
            "Sales order #"
        ]
        
        # UI components
        self.entries: Dict[str, ctk.CTkEntry] = {}
        self.model_dropdown: Optional[ModelDropdown] = None
        self.series_controls: Optional[SeriesControls] = None
        self.folder_label: Optional[ctk.CTkLabel] = None
        self.e3_instance_selector: Optional[E3InstanceSelector] = None
        
        # Data
        self.project_data = ProjectData()
        self.title_block_data = TitleBlockData()
        self.folder_selected = ""
        
        # Initialize the UI
        self.setup_ui()

        # After UI setup, check if an E3 instance was auto-selected
        # and load title block data if so
        self.root.after(1000, self._check_initial_e3_connection)
        
    def create_window(self, title: str = "Publish Project", geometry: str = "600x500") -> ctk.CTk:
        """
        Create and configure the main window.
        
        Args:
            title: Window title
            geometry: Window size
            
        Returns:
            Configured CTk root window
        """
        self.root = ctk.CTk()
        self.root.title(title)
        self.root.geometry(geometry)
        
        self.setup_ui()
        self.load_title_block_data()
        self.populate_fields()
        
        return self.root
        
    def setup_ui(self):
        """Set up the user interface components."""
        if not self.root:
            raise RuntimeError("Root window must be created first")
            
        # Main frame
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create E3 instance selector
        self._create_e3_instance_selector(main_frame)

        # Create form fields
        self._create_form_fields(main_frame)
        
        # Create model dropdown
        self._create_model_section(main_frame)
        
        # Create publishing options
        self._create_publishing_options(main_frame)
        
        # Create folder selection
        self._create_folder_section(main_frame)
        
        # Create publish button
        self._create_publish_button(main_frame)

    def _create_e3_instance_selector(self, parent):
        """Create the E3 instance selector section."""
        self.e3_instance_selector = E3InstanceSelector(
            parent,
            on_instance_changed=self._on_e3_instance_changed
        )
        self.e3_instance_selector.pack(fill="x", padx=5, pady=5)

    def _create_form_fields(self, parent):
        """Create the form input fields."""
        for field in self.fields:
            row_frame = ctk.CTkFrame(parent)
            row_frame.pack(fill="x", padx=5, pady=5)
            
            label = ctk.CTkLabel(row_frame, text=field + ":", width=120)
            label.pack(side="left", padx=5)
            
            entry = ctk.CTkEntry(row_frame, width=200)
            entry.pack(side="right", fill="x", expand=True, padx=5)
            self.entries[field] = entry
            
            # Add event handler for GSS Parent # field
            if field == "GSS Parent #":
                entry.bind('<KeyRelease>', self._on_gss_parent_changed)
                
    def _create_model_section(self, parent):
        """Create the model selection section."""
        model_frame = ctk.CTkFrame(parent)
        model_frame.pack(fill="x", padx=5, pady=5)
        
        model_label = ctk.CTkLabel(model_frame, text="Model:", width=120)
        model_label.pack(side="left", padx=5)
        
        self.model_dropdown = ModelDropdown(
            model_frame,
            values=["Select GSS Parent #"],
            width=200
        )
        self.model_dropdown.pack(side="right", fill="x", expand=True, padx=5)
        
    def _create_publishing_options(self, parent):
        """Create the publishing options section."""
        self.series_controls = SeriesControls(parent)
        self.series_controls.pack(fill="x", padx=5, pady=5)
        
    def _create_folder_section(self, parent):
        """Create the folder selection section."""
        # Browse button frame
        button_frame = ctk.CTkFrame(parent)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        browse_button = ctk.CTkButton(
            button_frame, 
            text="Browse", 
            command=self.browse_folder
        )
        browse_button.pack(side="left", padx=5)
        
        # Folder display frame
        folder_frame = ctk.CTkFrame(parent)
        folder_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.folder_label = ctk.CTkLabel(
            folder_frame, 
            text="No folder selected", 
            width=200, 
            wraplength=550, 
            justify="left"
        )
        self.folder_label.pack(fill="both", expand=True, padx=10, pady=5)
        
    def _create_publish_button(self, parent):
        """Create the publish button."""
        publish_button = ctk.CTkButton(
            parent, 
            text="Publish", 
            command=self.on_publish_clicked
        )
        publish_button.pack(side="bottom", padx=5, pady=10)
        
    def _on_gss_parent_changed(self, event=None):
        """Handle GSS Parent # field changes."""
        if self.model_service and self.model_dropdown:
            gss_number = self.entries["GSS Parent #"].get()
            models = self.model_service.get_models_for_gss(gss_number)
            self.model_dropdown.update_models(models)

    def _on_e3_instance_changed(self, instance):
        """Handle E3 instance selection changes."""
        try:
            self.logger.info(f"E3 instance changed to PID {instance['pid']}, Project: {instance.get('project_path', 'Unknown')}")

            # Clear existing data first
            self.logger.debug("Clearing form fields...")
            self._clear_form_fields()
            self.title_block_data = None

            # Force UI update
            self.root.update_idletasks()

            # Update E3 client to use the new instance
            if self.e3_client:
                # Force disconnect and reconnect to ensure fresh connection
                self.logger.debug("Disconnecting from current E3 instance...")
                self.e3_client.disconnect()

                self.logger.debug(f"Reconnecting to E3 instance PID {instance['pid']}...")
                success = self.e3_client.reconnect_to_instance(instance['pid'])
                if success:
                    self.logger.info(f"Successfully connected to E3 instance PID {instance['pid']}")

                    # Reload title block data from the new instance
                    self.logger.debug("Loading title block data...")
                    self.load_title_block_data()

                    if self.title_block_data:
                        self.logger.debug("Populating form fields with new data...")
                        self.populate_fields()

                        # Force UI update after populating
                        self.root.update_idletasks()

                        self.logger.info("Successfully reloaded data from new E3 instance")

                        # Debug: Print current field values
                        self._debug_current_field_values()
                    else:
                        self.logger.warning("No title block data found in selected E3 instance")
                else:
                    self.logger.error("Failed to connect to selected E3 instance")
                    ErrorDialog(
                        self.root,
                        "Connection Error",
                        f"Failed to connect to E3 instance (PID {instance['pid']})"
                    ).show()
            else:
                self.logger.warning("E3 client not available")

        except Exception as e:
            self.logger.error(f"Error switching E3 instance: {e}")
            ErrorDialog(
                self.root,
                "Instance Switch Error",
                f"Error switching to E3 instance:\n{e}"
            ).show()

    def _clear_form_fields(self):
        """Clear all form fields."""
        try:
            for field_name, entry in self.entries.items():
                entry.delete(0, 'end')
                self.logger.debug(f"Cleared field: {field_name}")

            if self.model_dropdown:
                self.model_dropdown.clear_selection()
                self.logger.debug("Cleared model dropdown")

            self.logger.debug("Cleared all form fields")
        except Exception as e:
            self.logger.error(f"Error clearing form fields: {e}")

    def _debug_current_field_values(self):
        """Debug method to print current field values."""
        try:
            self.logger.debug("=== Current Field Values ===")
            for field_name, entry in self.entries.items():
                current_value = entry.get()
                self.logger.debug(f"  {field_name}: '{current_value}'")

            if self.model_dropdown:
                model_value = self.model_dropdown.get()
                self.logger.debug(f"  Model: '{model_value}'")
            self.logger.debug("=== End Field Values ===")
        except Exception as e:
            self.logger.error(f"Error debugging field values: {e}")

    def _check_initial_e3_connection(self):
        """Check if an E3 instance was auto-selected and load data if needed."""
        try:
            if self.e3_instance_selector:
                selected_instance = self.e3_instance_selector.get_selected_instance()
                if selected_instance:
                    self.logger.info(f"Auto-selected E3 instance detected: PID {selected_instance['pid']}")

                    # Check if we already have title block data
                    if not self.title_block_data:
                        self.logger.info("No title block data loaded, attempting to load from selected instance")

                        # Trigger the instance changed callback to load data
                        self._on_e3_instance_changed(selected_instance)
                    else:
                        self.logger.info("Title block data already loaded")
                else:
                    self.logger.info("No E3 instance auto-selected")
            else:
                self.logger.warning("E3 instance selector not available")

        except Exception as e:
            self.logger.error(f"Error checking initial E3 connection: {e}")
            
    def load_title_block_data(self):
        """Load title block data from E3 project."""
        if not self.e3_client:
            self.logger.warning("E3 client not available - cannot load title block data")
            return

        # Check if an E3 instance is selected
        if self.e3_instance_selector and not self.e3_instance_selector.get_selected_instance():
            self.logger.info("No E3 instance selected - skipping title block data load")
            return

        try:
            # Ensure E3 client is connected
            if not self.e3_client.is_connected():
                if not self.e3_client.connect():
                    self.logger.error("Failed to connect to E3 Series")
                    return

            # Clear any existing title block data to ensure fresh read
            self.title_block_data = None

            # Read fresh title block data
            self.logger.debug("Reading title block data from E3...")
            self.title_block_data = self.e3_client.read_title_block_data()

            if self.title_block_data:
                self.logger.info(f"Successfully loaded title block data:")
                self.logger.info(f"  Document Number: {self.title_block_data.document_number}")
                self.logger.info(f"  Serial Number: {self.title_block_data.serial_number}")
                self.logger.info(f"  Customer: {self.title_block_data.customer}")
                self.logger.info(f"  Location: {self.title_block_data.location}")
                self.logger.info(f"  Description: {self.title_block_data.description}")
                self.logger.info(f"  Model: {self.title_block_data.model}")
                self.logger.info(f"  Sales Order: {self.title_block_data.sales_order}")
            else:
                self.logger.warning("No title block data returned from E3")

        except Exception as e:
            self.logger.error(f"Failed to load title block data: {e}")
            ErrorDialog(
                self.root,
                "E3 Connection Error",
                f"Failed to load project data from E3:\n{e}"
            ).show()
            
    def populate_fields(self):
        """Populate form fields with title block data."""
        if not self.title_block_data:
            self.logger.debug("No title block data available to populate fields")
            return

        self.logger.debug("Populating form fields with title block data")

        # Map title block data to form fields
        field_mapping = {
            "GSS Parent #": self.title_block_data.document_number,
            "Serial number": self.title_block_data.serial_number,
            "Customer": self.title_block_data.customer,
            "Location": self.title_block_data.location,
            "Title": self.title_block_data.description,
            "Sales order #": self.title_block_data.sales_order
        }

        # Debug the title block data
        self.logger.debug("=== Title Block Data Mapping ===")
        for field, value in field_mapping.items():
            self.logger.debug(f"  {field}: '{value}' (type: {type(value)})")
        self.logger.debug("=== End Title Block Mapping ===")

        # Populate fields - always clear and set, even if value is empty
        for field, value in field_mapping.items():
            if field in self.entries:
                entry = self.entries[field]
                entry.delete(0, 'end')
                if value:  # Only insert if value is not None/empty
                    entry.insert(0, value)
                    self.logger.debug(f"Set {field} = '{value}'")
                else:
                    self.logger.debug(f"Cleared {field} (no value)")

                # Force the entry to update
                entry.update_idletasks()

        # Update model dropdown after populating GSS Parent #
        self._on_gss_parent_changed()

        # Select matching model if available
        if (self.title_block_data.model and
            self.model_dropdown and
            self.model_dropdown.has_value(self.title_block_data.model)):
            self.model_dropdown.set(self.title_block_data.model)
            self.logger.debug(f"Set model dropdown to: {self.title_block_data.model}")
        else:
            if self.title_block_data.model:
                self.logger.debug(f"Model '{self.title_block_data.model}' not found in dropdown values: {self.model_dropdown.get_values() if self.model_dropdown else 'No dropdown'}")

        self.logger.info("Completed populating form fields")
            
    def browse_folder(self):
        """Open folder selection dialog."""
        folder = filedialog.askdirectory()
        if folder:
            self.folder_selected = folder
            self.folder_label.configure(
                text=f"Selected folder: {folder}", 
                wraplength=550
            )
            self.logger.info(f"Selected output folder: {folder}")
            
    def collect_form_data(self) -> ProjectData:
        """Collect data from form fields."""
        # Get raw values from form fields
        gss_parent = self.entries["GSS Parent #"].get().strip()
        serial_number = self.entries["Serial number"].get().strip()
        customer = self.entries["Customer"].get().strip()
        location = self.entries["Location"].get().strip()
        title = self.entries["Title"].get().strip()
        sales_order = self.entries["Sales order #"].get().strip()
        model = self.model_dropdown.get().strip() if self.model_dropdown else ""

        # Debug logging
        self.logger.debug("=== Collecting Form Data ===")
        self.logger.debug(f"GSS Parent #: '{gss_parent}'")
        self.logger.debug(f"Serial number: '{serial_number}'")
        self.logger.debug(f"Customer: '{customer}'")
        self.logger.debug(f"Location: '{location}'")
        self.logger.debug(f"Title: '{title}'")
        self.logger.debug(f"Sales order #: '{sales_order}'")
        self.logger.debug(f"Model: '{model}'")
        self.logger.debug("=== End Form Data ===")

        # Check for empty serial number and warn
        if not serial_number:
            self.logger.warning("Serial number is empty in form data!")
            # Don't set a default here - let validation handle it

        project_data = ProjectData(
            gss_parent=gss_parent,
            serial_number=serial_number,
            customer=customer,
            location=location,
            title=title,
            sales_order=sales_order,
            model=model,
            folder_path=self.folder_selected
        )

        return project_data
        
    def create_publish_config(self) -> PublishConfig:
        """Create publish configuration from UI settings."""
        config = PublishConfig()
        
        if self.series_controls:
            config.create_manual = self.series_controls.get_create_manual()
            config.output_base_path = self.folder_selected
            
        return config
        
    def on_publish_clicked(self):
        """Handle publish button click event."""
        try:
            # Collect form data
            project_data = self.collect_form_data()
            
            # Validate data
            validation_result = project_data.validate()
            if not validation_result.is_valid:
                error_msg = "Please fix the following errors:\n\n" + "\n".join(validation_result.errors)
                ErrorDialog(self.root, "Validation Error", error_msg).show()
                return
                
            # Show warnings if any
            if validation_result.has_warnings():
                warning_msg = "Please note the following warnings:\n\n" + "\n".join(validation_result.warnings)
                if not ConfirmationDialog(
                    self.root, 
                    "Validation Warnings", 
                    warning_msg + "\n\nDo you want to continue?"
                ).show():
                    return
                    
            # Create publish configuration
            publish_config = self.create_publish_config()
            
            # Check if series publishing is enabled
            if self.series_controls and self.series_controls.is_series_enabled():
                series_count = self.series_controls.get_series_count()
                self._publish_series(project_data, publish_config, series_count)
            else:
                self._publish_single(project_data, publish_config)
                
        except Exception as e:
            self.logger.error(f"Error during publish operation: {e}")
            ErrorDialog(
                self.root,
                "Publish Error", 
                f"An error occurred during publishing:\n{e}"
            ).show()
            
    def _publish_single(self, project_data: ProjectData, config: PublishConfig):
        """Publish a single project."""
        if not self.publish_service:
            ErrorDialog(
                self.root,
                "Service Error",
                "Publish service is not available"
            ).show()
            return
            
        try:
            self.logger.info(f"Starting single project publish: {project_data.gss_parent} - {project_data.serial_number}")
            
            result = self.publish_service.publish_project(project_data, config)
            
            if result.success:
                self._show_success_message(result)
            else:
                self._show_error_result(result)
                
        except Exception as e:
            self.logger.error(f"Failed to publish project: {e}")
            ErrorDialog(
                self.root,
                "Publish Failed",
                f"Failed to publish project:\n{e}"
            ).show()
            
    def _publish_series(self, project_data: ProjectData, config: PublishConfig, series_count: int):
        """Publish a series of projects."""
        if not self.publish_service:
            ErrorDialog(
                self.root,
                "Service Error", 
                "Publish service is not available"
            ).show()
            return
            
        try:
            self.logger.info(f"Starting series publish: {series_count} projects")
            
            results = self.publish_service.publish_series(project_data, config, series_count)
            
            successful_count = sum(1 for r in results if r.success)
            
            if successful_count == len(results):
                self._show_series_success_message(results)
            else:
                self._show_series_error_results(results)
                
        except Exception as e:
            self.logger.error(f"Failed to publish series: {e}")
            ErrorDialog(
                self.root,
                "Series Publish Failed",
                f"Failed to publish series:\n{e}"
            ).show()
            
    def _show_success_message(self, result):
        """Show success message for single project."""
        message = f"Project published successfully!\n\n"
        message += f"Serial Number: {result.serial_number}\n"
        message += f"Output Path: {result.output_path}\n"
        message += f"Steps Completed: {len(result.steps_completed)}"
        
        if result.warnings:
            message += f"\n\nWarnings:\n" + "\n".join(result.warnings)
            
        # Use confirmation dialog with dynamic sizing based on content
        ConfirmationDialog(
            self.root,
            "Publish Complete",
            message
        ).show()
        
    def _show_error_result(self, result):
        """Show error message for failed project."""
        message = f"Project publishing failed!\n\n"
        message += f"Serial Number: {result.serial_number}\n"
        message += f"Errors:\n" + "\n".join(result.errors)
        
        if result.warnings:
            message += f"\n\nWarnings:\n" + "\n".join(result.warnings)
            
        ErrorDialog(
            self.root,
            "Publish Failed",
            message
        ).show()
        
    def _show_series_success_message(self, results):
        """Show success message for series publishing."""
        message = f"Series published successfully!\n\n"
        message += f"Projects: {len(results)}\n"
        message += f"All projects completed without errors."
        
        ConfirmationDialog(
            self.root,
            "Series Publish Complete",
            message
        ).show()
        
    def _show_series_error_results(self, results):
        """Show error message for failed series publishing."""
        successful_count = sum(1 for r in results if r.success)
        failed_count = len(results) - successful_count
        
        message = f"Series publishing completed with errors!\n\n"
        message += f"Total Projects: {len(results)}\n"
        message += f"Successful: {successful_count}\n"
        message += f"Failed: {failed_count}\n\n"
        
        # Show details for failed projects
        failed_results = [r for r in results if not r.success]
        if failed_results:
            message += "Failed Projects:\n"
            for result in failed_results[:5]:  # Show first 5 failures
                message += f"- {result.serial_number}: {result.errors[0] if result.errors else 'Unknown error'}\n"
            if len(failed_results) > 5:
                message += f"... and {len(failed_results) - 5} more"
                
        ErrorDialog(
            self.root,
            "Series Publish Errors",
            message
        ).show()