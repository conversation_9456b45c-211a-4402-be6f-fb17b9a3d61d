["test/test_config.py::TestConfig::test_get_config_path", "test/test_config.py::TestConfig::test_get_config_value", "test/test_config.py::TestConfig::test_load_config", "test/test_config.py::TestConfig::test_load_models", "test/test_config.py::TestConfig::test_save_config", "test/test_config.py::TestConfig::test_save_models", "test/test_config.py::TestConfig::test_update_config", "test/test_config_integration.py::TestConfigIntegration::test_configuration_and_logging_integration", "test/test_config_integration.py::TestConfigIntegration::test_configuration_manager_with_logging", "test/test_configuration_manager.py::TestConfigurationManager::test_caching_behavior", "test/test_configuration_manager.py::TestConfigurationManager::test_get_all_categories", "test/test_configuration_manager.py::TestConfigurationManager::test_get_app_setting", "test/test_configuration_manager.py::TestConfigurationManager::test_get_model_config", "test/test_configuration_manager.py::TestConfigurationManager::test_get_models_for_category", "test/test_configuration_manager.py::TestConfigurationManager::test_init_custom_config_dir", "test/test_configuration_manager.py::TestConfigurationManager::test_init_default_config_dir", "test/test_configuration_manager.py::TestConfigurationManager::test_load_app_config_file_not_found", "test/test_configuration_manager.py::TestConfigurationManager::test_load_app_config_invalid_json", "test/test_configuration_manager.py::TestConfigurationManager::test_load_app_config_success", "test/test_configuration_manager.py::TestConfigurationManager::test_load_models_config_success", "test/test_configuration_manager.py::TestConfigurationManager::test_load_models_config_validation_error", "test/test_configuration_manager.py::TestConfigurationManager::test_reload_config", "test/test_configuration_manager.py::TestConfigurationManager::test_validate_model_data_invalid_types", "test/test_container.py::TestServiceContainer::test_clear_singletons", "test/test_container.py::TestServiceContainer::test_container_initialization", "test/test_container.py::TestServiceContainer::test_dispose", "test/test_container.py::TestServiceContainer::test_get_configuration_manager", "test/test_container.py::TestServiceContainer::test_get_model_service", "test/test_container.py::TestServiceContainer::test_get_publish_service", "test/test_container.py::TestServiceContainer::test_has_service", "test/test_container.py::TestServiceContainer::test_register_instance", "test/test_container.py::TestServiceContainer::test_service_not_registered_error", "test/test_container.py::TestServiceContainer::test_singleton_behavior", "test/test_dialog_requirements.py::TestDialogRequirements::test_confirmation_dialog_capabilities", "test/test_dialog_requirements.py::TestDialogRequirements::test_consistent_user_experience", "test/test_dialog_requirements.py::TestDialogRequirements::test_error_display_capabilities", "test/test_dialog_requirements.py::TestDialogRequirements::test_requirement_2_1_gui_separation", "test/test_dialog_requirements.py::TestDialogRequirements::test_requirement_3_4_meaningful_feedback", "test/test_dialogs.py::TestDialogComponents::test_base_dialog_initialization", "test/test_dialogs.py::TestDialogComponents::test_confirmation_dialog_initialization", "test/test_dialogs.py::TestDialogComponents::test_confirmation_dialog_simple_message", "test/test_dialogs.py::TestDialogComponents::test_consistent_interface", "test/test_dialogs.py::TestDialogComponents::test_error_dialog_initialization", "test/test_dialogs.py::TestDialogComponents::test_error_dialog_simple_message", "test/test_dialogs.py::TestDialogComponents::test_error_handling_requirement", "test/test_dialogs.py::TestDialogComponents::test_imports", "test/test_dialogs.py::TestDialogComponents::test_progress_dialog_initialization", "test/test_dialogs.py::TestDialogComponents::test_results_dialog_initialization", "test/test_dialogs.py::TestDialogComponents::test_validation_error_dialog_initialization", "test/test_e3_integration.py::TestDataModelIntegration::test_project_data_validation", "test/test_e3_integration.py::TestDataModelIntegration::test_title_block_to_project_data_conversion", "test/test_e3_integration.py::TestE3Client::test_connection_error_when_not_connected", "test/test_e3_integration.py::TestE3Client::test_init", "test/test_e3_integration.py::TestE3Client::test_is_connected_false_initially", "test/test_e3_integration.py::TestE3Client::test_validation_error_for_invalid_project_data", "test/test_e3_integration.py::TestReportGeneratorClient::test_export_gss_bom_validation", "test/test_e3_integration.py::TestReportGeneratorClient::test_init", "test/test_e3_integration.py::TestReportGeneratorClient::test_is_available", "test/test_e3_integration.py::TestReportGeneratorClient::test_validation_errors", "test/test_error_handler.py::TestDecorators::test_error_handler_decorator_success", "test/test_error_handler.py::TestDecorators::test_error_handler_decorator_with_error", "test/test_error_handler.py::TestDecorators::test_gui_error_handler_decorator", "test/test_error_handler.py::TestErrorHandler::test_get_user_message_for_known_error", "test/test_error_handler.py::TestErrorHandler::test_get_user_message_for_unknown_error", "test/test_error_handler.py::TestErrorHandler::test_handle_e3_connection_error", "test/test_error_handler.py::TestErrorHandler::test_handle_error_with_context", "test/test_error_handler.py::TestErrorHandler::test_handle_publish_error_with_context", "test/test_error_handler.py::TestErrorHandler::test_handle_validation_error", "test/test_error_handler.py::TestErrorHandler::test_initialization", "test/test_error_handler.py::TestErrorHandler::test_wrap_with_error_handling_no_callback", "test/test_error_handler.py::TestErrorHandler::test_wrap_with_error_handling_success", "test/test_error_handler.py::TestErrorHandler::test_wrap_with_error_handling_with_exception", "test/test_error_handler.py::TestErrorMiddleware::test_handle_operation_error_with_dialog", "test/test_error_handler.py::TestErrorMiddleware::test_handle_operation_error_without_dialog", "test/test_error_handler.py::TestErrorMiddleware::test_handle_publish_error_with_specialized_dialog", "test/test_error_handler.py::TestErrorMiddleware::test_initialization", "test/test_error_handler.py::TestErrorMiddleware::test_set_gui_error_handler", "test/test_error_handler.py::TestErrorMiddleware::test_wrap_gui_method_success", "test/test_error_handler.py::TestErrorMiddleware::test_wrap_gui_method_with_error", "test/test_error_handler.py::TestErrorRecovery::test_initialization", "test/test_error_handler.py::TestErrorRecovery::test_retry_operation_all_retries_fail", "test/test_error_handler.py::TestErrorRecovery::test_retry_operation_success_after_retries", "test/test_error_handler.py::TestErrorRecovery::test_retry_operation_success_first_try", "test/test_error_handler.py::TestErrorRecovery::test_safe_cleanup_all_succeed", "test/test_error_handler.py::TestErrorRecovery::test_safe_cleanup_some_fail", "test/test_error_handler.py::TestGUIErrorHandler::test_handle_gui_error_with_dialog", "test/test_error_handler.py::TestGUIErrorHandler::test_handle_gui_error_without_dialog", "test/test_error_handler.py::TestGUIErrorHandler::test_initialization", "test/test_error_handler.py::TestGUIErrorHandler::test_wrap_gui_operation_success", "test/test_error_handler.py::TestGUIErrorHandler::test_wrap_gui_operation_with_error", "test/test_error_handler.py::TestIntegration::test_end_to_end_error_handling", "test/test_error_handler.py::TestIntegration::test_error_context_preservation", "test/test_exceptions.py::TestConfigurationError::test_basic_initialization", "test/test_exceptions.py::TestConfigurationError::test_initialization_with_config_details", "test/test_exceptions.py::TestE3ConnectionError::test_basic_initialization", "test/test_exceptions.py::TestE3ConnectionError::test_initialization_with_operation", "test/test_exceptions.py::TestExceptionHierarchy::test_all_exceptions_inherit_from_publish_error", "test/test_exceptions.py::TestExceptionHierarchy::test_exception_context_preservation", "test/test_exceptions.py::TestExportError::test_basic_initialization", "test/test_exceptions.py::TestExportError::test_initialization_with_export_details", "test/test_exceptions.py::TestFileOperationError::test_basic_initialization", "test/test_exceptions.py::TestFileOperationError::test_initialization_with_file_details", "test/test_exceptions.py::TestGUIError::test_basic_initialization", "test/test_exceptions.py::TestGUIError::test_initialization_with_gui_details", "test/test_exceptions.py::TestIntegrationError::test_basic_initialization", "test/test_exceptions.py::TestIntegrationError::test_initialization_with_integration_details", "test/test_exceptions.py::TestPublishError::test_add_context", "test/test_exceptions.py::TestPublishError::test_basic_initialization", "test/test_exceptions.py::TestPublishError::test_context_chaining", "test/test_exceptions.py::TestPublishError::test_initialization_with_cause", "test/test_exceptions.py::TestPublishError::test_initialization_with_context", "test/test_exceptions.py::TestServiceError::test_basic_initialization", "test/test_exceptions.py::TestServiceError::test_initialization_with_service_details", "test/test_exceptions.py::TestValidationError::test_basic_initialization", "test/test_exceptions.py::TestValidationError::test_inheritance", "test/test_exceptions.py::TestValidationError::test_initialization_with_field_and_value", "test/test_export_service.py::TestExportError::test_init_basic", "test/test_export_service.py::TestExportError::test_init_with_operation", "test/test_export_service.py::TestExportError::test_init_with_original_error", "test/test_export_service.py::TestExportError::test_str_basic", "test/test_export_service.py::TestExportError::test_str_with_operation", "test/test_export_service.py::TestExportError::test_str_with_original_error", "test/test_export_service.py::TestExportResult::test_init", "test/test_export_service.py::TestExportResult::test_str_failure", "test/test_export_service.py::TestExportResult::test_str_success", "test/test_export_service.py::TestExportService::test_export_project_both_formats", "test/test_export_service.py::TestExportService::test_export_project_default_formats", "test/test_export_service.py::TestExportService::test_export_project_dxf_only", "test/test_export_service.py::TestExportService::test_export_project_no_dxf_files", "test/test_export_service.py::TestExportService::test_export_project_pdf_only", "test/test_export_service.py::TestExportService::test_export_project_with_errors", "test/test_export_service.py::TestExportService::test_export_to_dxf_creates_directory", "test/test_export_service.py::TestExportService::test_export_to_dxf_missing_gss_parent", "test/test_export_service.py::TestExportService::test_export_to_dxf_missing_serial_number", "test/test_export_service.py::TestExportService::test_export_to_dxf_no_client", "test/test_export_service.py::TestExportService::test_export_to_dxf_no_files", "test/test_export_service.py::TestExportService::test_export_to_dxf_no_project_data", "test/test_export_service.py::TestExportService::test_export_to_dxf_success", "test/test_export_service.py::TestExportService::test_export_to_pdf_creates_directory", "test/test_export_service.py::TestExportService::test_export_to_pdf_e3_connection_error", "test/test_export_service.py::TestExportService::test_export_to_pdf_e3_operation_error", "test/test_export_service.py::TestExportService::test_export_to_pdf_no_client", "test/test_export_service.py::TestExportService::test_export_to_pdf_no_project_data", "test/test_export_service.py::TestExportService::test_export_to_pdf_success", "test/test_export_service.py::TestExportService::test_init_with_client", "test/test_export_service.py::TestExportService::test_init_without_client", "test/test_file_operations.py::TestFileOperations::test_copy_file_nonexistent_source", "test/test_file_operations.py::TestFileOperations::test_copy_file_success", "test/test_file_operations.py::TestFileOperations::test_create_serial_folder_empty_serial", "test/test_file_operations.py::TestFileOperations::test_create_serial_folder_invalid_base_path", "test/test_file_operations.py::TestFileOperations::test_create_serial_folder_sanitizes_filename", "test/test_file_operations.py::TestFileOperations::test_create_serial_folder_success", "test/test_file_operations.py::TestFileOperations::test_ensure_directory_exists", "test/test_file_operations.py::TestFileOperations::test_get_file_size", "test/test_file_operations.py::TestFileOperations::test_list_files_in_directory", "test/test_file_operations.py::TestFileOperations::test_sanitize_filename", "test/test_file_operations.py::TestFileOperations::test_save_job_data_json_creates_directory", "test/test_file_operations.py::TestFileOperations::test_save_job_data_json_success", "test/test_file_operations.py::TestFileOperations::test_validate_path_empty_path", "test/test_file_operations.py::TestFileOperations::test_validate_path_none_path", "test/test_file_operations.py::TestFileOperations::test_validate_path_valid_existing_path", "test/test_gui_integration.py::TestGUIIntegration::test_class_definitions", "test/test_gui_integration.py::TestGUIIntegration::test_imports", "test/test_gui_integration.py::TestGUIIntegration::test_module_structure", "test/test_gui_integration.py::TestGUIIntegration::test_requirements_compliance", "test/test_gui_widgets.py::TestFormField::test_get_set_methods", "test/test_gui_widgets.py::TestFormField::test_initialization", "test/test_gui_widgets.py::TestModelDropdown::test_get_selected_model", "test/test_gui_widgets.py::TestModelDropdown::test_has_valid_selection", "test/test_gui_widgets.py::TestModelDropdown::test_initialization", "test/test_gui_widgets.py::TestModelDropdown::test_update_models", "test/test_gui_widgets.py::TestSeriesControls::test_get_create_manual", "test/test_gui_widgets.py::TestSeriesControls::test_get_series_count", "test/test_gui_widgets.py::TestSeriesControls::test_initialization", "test/test_gui_widgets.py::TestSeriesControls::test_validate_series_count", "test/test_init.py::TestInit::test_applications_init", "test/test_init.py::TestInit::test_lib_init", "test/test_integration.py::TestDependencyInjectionIntegration::test_application_initialization", "test/test_integration.py::TestDependencyInjectionIntegration::test_cleanup", "test/test_integration.py::TestDependencyInjectionIntegration::test_container_initialization", "test/test_integration.py::TestDependencyInjectionIntegration::test_frozen_environment_handling", "test/test_integration.py::TestDependencyInjectionIntegration::test_gui_creation_without_mainloop", "test/test_integration.py::TestDependencyInjectionIntegration::test_logging_setup", "test/test_logging_config.py::TestLoggingConfig::test_get_logger", "test/test_logging_config.py::TestLoggingConfig::test_setup_logging", "test/test_manual_service.py::TestManualError::test_init_basic", "test/test_manual_service.py::TestManualError::test_init_with_details", "test/test_manual_service.py::TestManualError::test_init_with_operation", "test/test_manual_service.py::TestManualError::test_init_with_original_error", "test/test_manual_service.py::TestManualError::test_str_basic", "test/test_manual_service.py::TestManualError::test_str_with_operation", "test/test_manual_service.py::TestManualError::test_str_with_original_error", "test/test_manual_service.py::TestManualResult::test_init", "test/test_manual_service.py::TestManualResult::test_str_failure", "test/test_manual_service.py::TestManualResult::test_str_success", "test/test_manual_service.py::TestManualService::test_can_create_manual_for_model_false", "test/test_manual_service.py::TestManualService::test_can_create_manual_for_model_not_available", "test/test_manual_service.py::TestManualService::test_can_create_manual_for_model_true", "test/test_manual_service.py::TestManualService::test_create_manual_creator_exception", "test/test_manual_service.py::TestManualService::test_create_manual_invalid_project_data", "test/test_manual_service.py::TestManualService::test_create_manual_not_available", "test/test_manual_service.py::TestManualService::test_create_manual_process_exception", "test/test_manual_service.py::TestManualService::test_create_manual_success", "test/test_manual_service.py::TestManualService::test_create_manual_unknown_model", "test/test_manual_service.py::TestManualService::test_determine_category_found", "test/test_manual_service.py::TestManualService::test_determine_category_not_found", "test/test_manual_service.py::TestManualService::test_get_expected_manual_files", "test/test_manual_service.py::TestManualService::test_get_model_data_dict_format", "test/test_manual_service.py::TestManualService::test_get_model_data_legacy_format", "test/test_manual_service.py::TestManualService::test_get_model_data_not_found", "test/test_manual_service.py::TestManualService::test_get_supported_models", "test/test_manual_service.py::TestManualService::test_get_supported_models_empty_config", "test/test_manual_service.py::TestManualService::test_init_with_config", "test/test_manual_service.py::TestManualService::test_init_without_config", "test/test_manual_service.py::TestManualService::test_is_manual_creation_available_false", "test/test_manual_service.py::TestManualService::test_is_manual_creation_available_true", "test/test_manual_service.py::TestManualService::test_validate_project_data_missing_gss_parent", "test/test_manual_service.py::TestManualService::test_validate_project_data_missing_model", "test/test_manual_service.py::TestManualService::test_validate_project_data_missing_serial_number", "test/test_manual_service.py::TestManualService::test_validate_project_data_unknown_model", "test/test_manual_service.py::TestManualService::test_validate_project_data_valid", "test/test_manual_service.py::TestValidationResult::test_init", "test/test_model_service.py::TestModelData::test_init_with_all_parameters", "test/test_model_service.py::TestModelData::test_init_with_defaults", "test/test_model_service.py::TestModelService::test_get_model_data_empty_string", "test/test_model_service.py::TestModelService::test_get_model_data_found", "test/test_model_service.py::TestModelService::test_get_model_data_not_found", "test/test_model_service.py::TestModelService::test_get_models_for_gss_empty_string", "test/test_model_service.py::TestModelService::test_get_models_for_gss_found", "test/test_model_service.py::TestModelService::test_get_models_for_gss_not_found", "test/test_model_service.py::TestModelService::test_get_models_for_gss_single_match", "test/test_model_service.py::TestModelService::test_init_with_config", "test/test_model_service.py::TestModelService::test_init_without_config", "test/test_model_service.py::TestModelService::test_legacy_format_support", "test/test_model_service.py::TestModelService::test_logging_debug_messages", "test/test_model_service.py::TestModelService::test_logging_warning_for_missing_model", "test/test_project_data.py::TestProjectData::test_from_dict", "test/test_project_data.py::TestProjectData::test_is_empty", "test/test_project_data.py::TestProjectData::test_project_data_creation", "test/test_project_data.py::TestProjectData::test_project_data_post_init_strips_whitespace", "test/test_project_data.py::TestProjectData::test_to_dict", "test/test_project_data.py::TestProjectData::test_validate_field_length_limits", "test/test_project_data.py::TestProjectData::test_validate_missing_required_fields", "test/test_project_data.py::TestProjectData::test_validate_valid_data", "test/test_project_data.py::TestTitleBlockData::test_is_valid", "test/test_project_data.py::TestTitleBlockData::test_title_block_creation", "test/test_project_data.py::TestTitleBlockData::test_title_block_post_init_strips_whitespace", "test/test_project_data.py::TestTitleBlockData::test_to_project_data", "test/test_project_data.py::TestTitleBlockData::test_to_project_data_no_override", "test/test_project_data.py::TestValidationResult::test_add_error", "test/test_project_data.py::TestValidationResult::test_add_warning", "test/test_project_data.py::TestValidationResult::test_get_summary", "test/test_project_data.py::TestValidationResult::test_has_errors", "test/test_project_data.py::TestValidationResult::test_has_warnings", "test/test_project_data.py::TestValidationResult::test_validation_result_creation", "test/test_publish_config.py::TestModelData::test_from_dict", "test/test_publish_config.py::TestModelData::test_has_drawings", "test/test_publish_config.py::TestModelData::test_has_manual_template", "test/test_publish_config.py::TestModelData::test_model_data_creation", "test/test_publish_config.py::TestModelData::test_model_data_post_init_strips_whitespace", "test/test_publish_config.py::TestModelData::test_to_dict", "test/test_publish_config.py::TestModelData::test_validate_missing_controls_parent", "test/test_publish_config.py::TestModelData::test_validate_missing_template", "test/test_publish_config.py::TestModelData::test_validate_nonexistent_template", "test/test_publish_config.py::TestModelData::test_validate_valid_model", "test/test_publish_config.py::TestModelsConfiguration::test_from_dict", "test/test_publish_config.py::TestModelsConfiguration::test_get_all_models", "test/test_publish_config.py::TestModelsConfiguration::test_get_categories", "test/test_publish_config.py::TestModelsConfiguration::test_get_model", "test/test_publish_config.py::TestModelsConfiguration::test_get_models_for_gss", "test/test_publish_config.py::TestModelsConfiguration::test_models_configuration_creation", "test/test_publish_config.py::TestModelsConfiguration::test_validate_all", "test/test_publish_config.py::TestPublishConfig::test_from_dict", "test/test_publish_config.py::TestPublishConfig::test_publish_config_creation", "test/test_publish_config.py::TestPublishConfig::test_publish_config_post_init_format_validation", "test/test_publish_config.py::TestPublishConfig::test_publish_config_with_values", "test/test_publish_config.py::TestPublishConfig::test_to_dict", "test/test_publish_config.py::TestPublishConfig::test_validate_empty_export_formats", "test/test_publish_config.py::TestPublishConfig::test_validate_invalid_series_count", "test/test_publish_config.py::TestPublishConfig::test_validate_large_series_count_warning", "test/test_publish_config.py::TestPublishConfig::test_validate_valid_config", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_get_publish_logger", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_log_operation_error", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_log_operation_start", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_log_operation_start_no_kwargs", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_log_operation_success", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_setup_publish_logging_custom_config", "test/test_publish_logging_config.py::TestConvenienceFunctions::test_setup_publish_logging_default_config", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_configure_module_logging", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_get_logger", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_get_logger_auto_initialize", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_set_log_level_root_logger", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_set_log_level_specific_logger", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_setup_logging_already_initialized", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_setup_logging_custom_params", "test/test_publish_logging_config.py::TestPublishLoggingConfig::test_setup_logging_success", "test/test_publish_service.py::TestPublishConfig::test_config_modification", "test/test_publish_service.py::TestPublishConfig::test_init_defaults", "test/test_publish_service.py::TestPublishError::test_init_basic", "test/test_publish_service.py::TestPublishError::test_init_with_operation", "test/test_publish_service.py::TestPublishError::test_init_with_original_error", "test/test_publish_service.py::TestPublishError::test_str_basic", "test/test_publish_service.py::TestPublishError::test_str_with_operation", "test/test_publish_service.py::TestPublishError::test_str_with_original_error", "test/test_publish_service.py::TestPublishResult::test_init_defaults", "test/test_publish_service.py::TestPublishResult::test_init_with_parameters", "test/test_publish_service.py::TestPublishResult::test_str_failure", "test/test_publish_service.py::TestPublishResult::test_str_success", "test/test_publish_service.py::TestPublishService::test_create_output_folder", "test/test_publish_service.py::TestPublishService::test_create_output_folder_no_base_path", "test/test_publish_service.py::TestPublishService::test_create_series_project_data", "test/test_publish_service.py::TestPublishService::test_increment_serial_number_alphanumeric", "test/test_publish_service.py::TestPublishService::test_increment_serial_number_numeric", "test/test_publish_service.py::TestPublishService::test_init_with_all_services", "test/test_publish_service.py::TestPublishService::test_init_with_minimal_services", "test/test_publish_service.py::TestPublishService::test_publish_project_e3_error_continue", "test/test_publish_service.py::TestPublishService::test_publish_project_e3_error_fail_fast", "test/test_publish_service.py::TestPublishService::test_publish_project_export_error", "test/test_publish_service.py::TestPublishService::test_publish_project_manual_error", "test/test_publish_service.py::TestPublishService::test_publish_project_manual_not_supported", "test/test_publish_service.py::TestPublishService::test_publish_project_no_config", "test/test_publish_service.py::TestPublishService::test_publish_project_no_project_data", "test/test_publish_service.py::TestPublishService::test_publish_project_no_services", "test/test_publish_service.py::TestPublishService::test_publish_project_report_error", "test/test_publish_service.py::TestPublishService::test_publish_project_success_full_workflow", "test/test_publish_service.py::TestPublishService::test_publish_project_validation_error", "test/test_publish_service.py::TestPublishService::test_publish_series_invalid_count", "test/test_publish_service.py::TestPublishService::test_publish_series_stop_on_error", "test/test_publish_service.py::TestPublishService::test_publish_series_success", "test/test_publish_service.py::TestPublishService::test_publish_series_with_errors_continue", "test/test_publish_service.py::TestPublishService::test_save_job_data_json", "test/test_publish_service.py::TestPublishService::test_validate_publish_inputs_missing_serial", "test/test_publish_service.py::TestPublishService::test_validate_publish_inputs_valid", "test/test_publishing_workflow_integration.py::TestComponentInteractionIntegration::test_configuration_consistency_across_components", "test/test_publishing_workflow_integration.py::TestComponentInteractionIntegration::test_error_propagation_between_components", "test/test_publishing_workflow_integration.py::TestComponentInteractionIntegration::test_service_dependency_injection", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_complete_publishing_workflow_success", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_configuration_validation_integration", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_data_flow_integration", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_error_handling_integration", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_model_service_integration_with_project_data", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_resource_cleanup_integration", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_series_publishing_workflow", "test/test_publishing_workflow_integration.py::TestPublishingWorkflowIntegration::test_title_block_to_project_data_workflow", "test/test_series_generator.py::TestSeriesGenerator::test_generate_series_invalid_count", "test/test_series_generator.py::TestSeriesGenerator::test_generate_series_numeric", "test/test_series_generator.py::TestSeriesGenerator::test_increment_numeric_serial", "test/test_series_generator.py::TestSeriesGenerator::test_increment_serial_basic", "test/test_series_generator.py::TestSeriesGenerator::test_increment_serial_edge_cases", "test/test_series_generator.py::TestSeriesGenerator::test_validate_serial_format", "test/test_setup.py::TestSetup::test_setup_py_exists", "test/test_setup.py::TestSetup::test_setup_py_is_valid_python", "test/test_setup.py::TestSetup::test_setup_py_sdist", "test/test_utils.py::TestUtils::test_convert_to_absolute_path", "test/test_utils.py::TestUtils::test_convert_to_relative_path", "test/test_utils.py::TestUtils::test_ensure_dir_exists", "test/test_utils.py::TestUtils::test_get_app_dir", "test/test_utils.py::TestUtils::test_get_config_path", "test/test_utils.py::TestUtils::test_get_resource_path", "test/test_utils.py::TestUtils::test_get_user_data_dir", "test/test_utils.py::TestUtils::test_load_json_config", "test/test_utils.py::TestUtils::test_save_json_config", "test/test_validation.py::TestEmailValidation::test_empty_email", "test/test_validation.py::TestEmailValidation::test_invalid_email_addresses", "test/test_validation.py::TestEmailValidation::test_valid_email_addresses", "test/test_validation.py::TestExportFormatsValidation::test_empty_export_formats", "test/test_validation.py::TestExportFormatsValidation::test_invalid_export_formats", "test/test_validation.py::TestExportFormatsValidation::test_valid_export_formats", "test/test_validation.py::TestFilePathValidation::test_empty_file_path", "test/test_validation.py::TestFilePathValidation::test_invalid_characters", "test/test_validation.py::TestFilePathValidation::test_nonexistent_path", "test/test_validation.py::TestFilePathValidation::test_path_too_long", "test/test_validation.py::TestFilePathValidation::test_valid_file_path", "test/test_validation.py::TestFilenameSanitization::test_sanitize_empty_filename", "test/test_validation.py::TestFilenameSanitization::test_sanitize_invalid_characters", "test/test_validation.py::TestFilenameSanitization::test_sanitize_long_filename", "test/test_validation.py::TestFilenameSanitization::test_sanitize_valid_filename", "test/test_validation.py::TestGSSParentValidation::test_empty_gss_parent", "test/test_validation.py::TestGSSParentValidation::test_hyphen_patterns", "test/test_validation.py::TestGSSParentValidation::test_invalid_characters", "test/test_validation.py::TestGSSParentValidation::test_length_constraints", "test/test_validation.py::TestGSSParentValidation::test_valid_gss_parent", "test/test_validation.py::TestModelNameValidation::test_empty_model_name", "test/test_validation.py::TestModelNameValidation::test_model_in_available_list", "test/test_validation.py::TestModelNameValidation::test_model_not_in_available_list", "test/test_validation.py::TestModelNameValidation::test_valid_model_name", "test/test_validation.py::TestProjectDataValidation::test_validate_and_sanitize_invalid_data", "test/test_validation.py::TestProjectDataValidation::test_validate_and_sanitize_valid_data", "test/test_validation.py::TestSerialNumberValidation::test_empty_serial_number", "test/test_validation.py::TestSerialNumberValidation::test_invalid_characters", "test/test_validation.py::TestSerialNumberValidation::test_length_constraints", "test/test_validation.py::TestSerialNumberValidation::test_valid_serial_numbers", "test/test_validation.py::TestSeriesCountValidation::test_invalid_series_count_type", "test/test_validation.py::TestSeriesCountValidation::test_series_count_too_large", "test/test_validation.py::TestSeriesCountValidation::test_series_count_too_small", "test/test_validation.py::TestSeriesCountValidation::test_valid_series_count", "test/test_validation.py::TestTemplatePathValidation::test_empty_template_path", "test/test_validation.py::TestTemplatePathValidation::test_invalid_template_extension", "test/test_validation.py::TestTemplatePathValidation::test_nonexistent_template_path", "test/test_validation.py::TestTemplatePathValidation::test_valid_template_path", "test/test_validation.py::TestValidationExceptions::test_configuration_error", "test/test_validation.py::TestValidationExceptions::test_publish_error_hierarchy", "test/test_validation.py::TestValidationExceptions::test_validation_error"]