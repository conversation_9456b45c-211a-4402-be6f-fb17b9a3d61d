"""
Custom widgets for the publish application.

This module contains reusable UI components that can be used across
different parts of the application. All widgets are decoupled from
business logic and can be used independently.
"""

import customtkinter as ctk
from typing import List, Optional, Callable, Tuple, Dict, Any
import logging
import sys
import os

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)


class E3InstanceSelector(ctk.CTkFrame):
    """Custom frame for selecting E3 Series instances."""

    def __init__(self, parent, on_instance_changed: Optional[Callable] = None, **kwargs):
        """
        Initialize the E3 instance selector.

        Args:
            parent: Parent widget
            on_instance_changed: Callback function when instance selection changes
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        self.logger = logging.getLogger(__name__)
        self.on_instance_changed = on_instance_changed
        self.instances = []
        self.selected_instance = None

        # Create controls
        self._create_controls()

        # Load initial instances
        self.refresh_instances()

    def _create_controls(self):
        """Create the instance selector widgets."""
        # Title label
        title_label = ctk.CTkLabel(self, text="E3 Series Instance:", font=ctk.CTkFont(weight="bold"))
        title_label.pack(side="left", padx=5)

        # Instance dropdown
        self.instance_dropdown = ctk.CTkComboBox(
            self,
            values=["No instances found"],
            command=self._on_instance_selected,
            width=300
        )
        self.instance_dropdown.pack(side="left", padx=5, fill="x", expand=True)

        # Refresh button
        self.refresh_button = ctk.CTkButton(
            self,
            text="Refresh",
            command=self.refresh_instances,
            width=80
        )
        self.refresh_button.pack(side="right", padx=5)

    def _on_instance_selected(self, selection: str):
        """Handle instance selection change."""
        try:
            if not self.instances or selection == "No instances found":
                self.selected_instance = None
                self.logger.debug("No valid instance selected")
                return

            # Find the selected instance
            for instance in self.instances:
                if self._format_instance_display(instance) == selection:
                    # Check if this is actually a different instance
                    if (self.selected_instance and
                        self.selected_instance['pid'] == instance['pid']):
                        self.logger.debug(f"Same instance selected (PID {instance['pid']}), skipping callback")
                        return

                    self.selected_instance = instance
                    self.logger.info(f"Selected E3 instance: PID {instance['pid']}, Project: {instance.get('project_path', 'Unknown')}")

                    # Notify callback
                    if self.on_instance_changed:
                        self.logger.debug("Calling instance changed callback")
                        try:
                            self.on_instance_changed(instance)
                        except Exception as callback_error:
                            self.logger.error(f"Error in instance changed callback: {callback_error}")
                    else:
                        self.logger.debug("No instance changed callback registered")
                    break
            else:
                self.logger.warning(f"Could not find instance for selection: {selection}")

        except Exception as e:
            self.logger.error(f"Error handling instance selection: {e}")

    def refresh_instances(self):
        """Refresh the list of available E3 instances."""
        try:
            self.logger.debug("Refreshing E3 instances...")

            # Import the connection manager
            from lib.e3_connection_manager import E3ConnectionManager

            # Get running instances
            manager = E3ConnectionManager(self.logger)
            raw_instances = manager.get_running_e3_instances()

            # Convert to dict format for easier handling
            self.instances = []
            for instance in raw_instances:
                instance_dict = {
                    'pid': instance.pid,
                    'name': instance.name,
                    'project_path': instance.project_path or "Unknown Project"
                }
                self.instances.append(instance_dict)

            # Update dropdown
            if self.instances:
                display_values = [self._format_instance_display(inst) for inst in self.instances]
                self.instance_dropdown.configure(values=display_values)

                # Auto-select first instance if none selected
                if not self.selected_instance and self.instances:
                    self.instance_dropdown.set(display_values[0])
                    # Delay the callback slightly to ensure parent window is ready
                    if hasattr(self, 'master') and hasattr(self.master, 'after'):
                        self.master.after(100, lambda: self._on_instance_selected(display_values[0]))
                    else:
                        self._on_instance_selected(display_values[0])

                self.logger.info(f"Found {len(self.instances)} E3 instances")
            else:
                self.instance_dropdown.configure(values=["No instances found"])
                self.instance_dropdown.set("No instances found")
                self.selected_instance = None
                self.logger.warning("No E3 instances found")

        except Exception as e:
            self.logger.error(f"Error refreshing E3 instances: {e}")
            self.instance_dropdown.configure(values=["Error loading instances"])
            self.instance_dropdown.set("Error loading instances")
            self.selected_instance = None

    def _format_instance_display(self, instance: Dict[str, Any]) -> str:
        """Format instance information for display."""
        project_name = os.path.basename(instance.get('project_path', 'Unknown'))
        if project_name == 'Unknown':
            project_name = instance.get('project_path', 'Unknown Project')
        return f"PID {instance['pid']} - {project_name}"

    def get_selected_instance(self) -> Optional[Dict[str, Any]]:
        """Get the currently selected instance."""
        return self.selected_instance

    def get_selected_pid(self) -> Optional[int]:
        """Get the PID of the currently selected instance."""
        return self.selected_instance['pid'] if self.selected_instance else None


class ModelDropdown(ctk.CTkOptionMenu):
    """Custom dropdown for model selection."""

    def __init__(self, parent, values: List[str] = None,
                 command: Optional[Callable] = None, **kwargs):
        """
        Initialize the model dropdown.

        Args:
            parent: Parent widget
            values: Initial list of values
            command: Callback function for selection changes
            **kwargs: Additional arguments for CTkOptionMenu
        """
        self.logger = logging.getLogger(__name__)

        # Set default values if none provided
        if values is None:
            values = ["Select GSS Parent #"]

        # Store values internally for easier access
        self._current_values = values.copy()

        # Initialize with default values
        super().__init__(parent, values=values, command=command, **kwargs)

        # Store original command for chaining
        self._user_command = command

        # Set default selection
        if values:
            self.set(values[0])
            
    def update_models(self, models: List[str]):
        """
        Update the available models in the dropdown.

        Args:
            models: List of model names to display
        """
        self.logger.debug(f"Updating model dropdown with {len(models)} models")

        if models:
            # Store values internally
            self._current_values = models.copy()
            # Configure with new model list
            self.configure(values=models)
            # Set first model as default
            self.set(models[0])
            self.logger.debug(f"Set default model to: {models[0]}")
        else:
            # No models available
            no_models_text = "No matching models"
            self._current_values = [no_models_text]
            self.configure(values=[no_models_text])
            self.set(no_models_text)
            self.logger.debug("No models available for current GSS parent")
            
    def get_selected_model(self) -> str:
        """
        Get the currently selected model.
        
        Returns:
            Currently selected model name
        """
        current_value = self.get()
        
        # Return empty string for placeholder values
        if current_value in ["Select GSS Parent #", "No matching models"]:
            return ""
            
        return current_value
        
    def has_valid_selection(self) -> bool:
        """
        Check if a valid model is selected.
        
        Returns:
            True if a valid model is selected
        """
        return bool(self.get_selected_model())
        
    def clear_selection(self):
        """Clear the current selection."""
        default_text = "Select GSS Parent #"
        self._current_values = [default_text]
        self.configure(values=[default_text])
        self.set(default_text)

    def has_value(self, value: str) -> bool:
        """Check if a value exists in the dropdown."""
        return value in self._current_values

    def get_values(self) -> List[str]:
        """Get the current values in the dropdown."""
        return self._current_values.copy()


class SeriesControls(ctk.CTkFrame):
    """Controls for series publishing functionality."""
    
    def __init__(self, parent, **kwargs):
        """
        Initialize the series controls.
        
        Args:
            parent: Parent widget
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        self.logger = logging.getLogger(__name__)
        
        # Control variables
        self.create_manual_var = ctk.BooleanVar(value=False)
        self.fill_series_var = ctk.BooleanVar(value=False)
        
        # UI components
        self.manual_checkbox: Optional[ctk.CTkCheckBox] = None
        self.series_checkbox: Optional[ctk.CTkCheckBox] = None
        self.series_count_entry: Optional[ctk.CTkEntry] = None
        
        self.setup_controls()
        
    def setup_controls(self):
        """Set up the series control widgets."""
        # Manual creation checkbox
        manual_frame = ctk.CTkFrame(self)
        manual_frame.pack(fill="x", padx=5, pady=2)
        
        self.manual_checkbox = ctk.CTkCheckBox(
            manual_frame,
            text="Create Manual",
            variable=self.create_manual_var,
            command=self._on_manual_checkbox_changed
        )
        self.manual_checkbox.pack(side="left", padx=5)
        
        # Series publishing controls
        series_frame = ctk.CTkFrame(self)
        series_frame.pack(fill="x", padx=5, pady=2)
        
        self.series_checkbox = ctk.CTkCheckBox(
            series_frame,
            text="Fill Series",
            variable=self.fill_series_var,
            command=self._on_series_checkbox_changed
        )
        self.series_checkbox.pack(side="left", padx=5)
        
        # Series count entry
        count_label = ctk.CTkLabel(series_frame, text="Count:", width=50)
        count_label.pack(side="left", padx=(20, 5))
        
        self.series_count_entry = ctk.CTkEntry(
            series_frame, 
            width=80, 
            placeholder_text="1"
        )
        self.series_count_entry.pack(side="left", padx=5)
        self.series_count_entry.insert(0, "1")  # Default value
        
        # Initially disable count entry
        self.series_count_entry.configure(state="disabled")
        
    def _on_manual_checkbox_changed(self):
        """Handle manual creation checkbox changes."""
        is_checked = self.create_manual_var.get()
        self.logger.debug(f"Manual creation checkbox changed: {is_checked}")
        
    def _on_series_checkbox_changed(self):
        """Handle series checkbox changes."""
        is_checked = self.fill_series_var.get()
        self.logger.debug(f"Series checkbox changed: {is_checked}")
        
        # Enable/disable count entry based on checkbox state
        if is_checked:
            self.series_count_entry.configure(state="normal")
            # Focus on the entry and select all text for easy editing
            self.series_count_entry.focus()
            self.series_count_entry.select_range(0, 'end')
        else:
            self.series_count_entry.configure(state="disabled")
            
    def get_create_manual(self) -> bool:
        """
        Get the create manual setting.
        
        Returns:
            True if manual creation is enabled
        """
        return self.create_manual_var.get()
        
    def set_create_manual(self, value: bool):
        """
        Set the create manual setting.
        
        Args:
            value: Whether to enable manual creation
        """
        self.create_manual_var.set(value)
        
    def is_series_enabled(self) -> bool:
        """
        Check if series publishing is enabled.
        
        Returns:
            True if series publishing is enabled
        """
        return self.fill_series_var.get()
        
    def set_series_enabled(self, value: bool):
        """
        Set the series publishing setting.
        
        Args:
            value: Whether to enable series publishing
        """
        self.fill_series_var.set(value)
        self._on_series_checkbox_changed()  # Update UI state
        
    def get_series_count(self) -> int:
        """
        Get the series count value.
        
        Returns:
            Number of projects in series (minimum 1)
        """
        try:
            count_text = self.series_count_entry.get().strip()
            if not count_text:
                return 1
                
            count = int(count_text)
            return max(1, count)  # Ensure minimum of 1
        except (ValueError, AttributeError):
            self.logger.warning("Invalid series count value, defaulting to 1")
            return 1
            
    def set_series_count(self, count: int):
        """
        Set the series count value.
        
        Args:
            count: Number of projects in series
        """
        if count < 1:
            count = 1
            
        self.series_count_entry.delete(0, 'end')
        self.series_count_entry.insert(0, str(count))
        
    def validate_series_count(self) -> tuple[bool, str]:
        """
        Validate the series count input.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not self.is_series_enabled():
            return True, ""
            
        try:
            count_text = self.series_count_entry.get().strip()
            if not count_text:
                return False, "Series count cannot be empty"
                
            count = int(count_text)
            if count < 1:
                return False, "Series count must be at least 1"
            if count > 1000:
                return False, "Series count cannot exceed 1000"
                
            return True, ""
        except ValueError:
            return False, "Series count must be a valid number"
            
    def get_configuration(self) -> dict:
        """
        Get the current configuration as a dictionary.
        
        Returns:
            Dictionary containing current settings
        """
        return {
            "create_manual": self.get_create_manual(),
            "series_enabled": self.is_series_enabled(),
            "series_count": self.get_series_count()
        }
        
    def set_configuration(self, config: dict):
        """
        Set configuration from a dictionary.
        
        Args:
            config: Dictionary containing settings
        """
        if "create_manual" in config:
            self.set_create_manual(config["create_manual"])
            
        if "series_enabled" in config:
            self.set_series_enabled(config["series_enabled"])
            
        if "series_count" in config:
            self.set_series_count(config["series_count"])


class FormField(ctk.CTkFrame):
    """Reusable form field widget with label and entry."""
    
    def __init__(self, parent, label_text: str, entry_width: int = 200,
                 label_width: int = 120, **kwargs):
        """
        Initialize the form field.
        
        Args:
            parent: Parent widget
            label_text: Text for the label
            entry_width: Width of the entry widget
            label_width: Width of the label widget
            **kwargs: Additional arguments for CTkFrame
        """
        super().__init__(parent, **kwargs)
        
        self.label_text = label_text
        
        # Create label
        self.label = ctk.CTkLabel(
            self, 
            text=label_text + ":", 
            width=label_width
        )
        self.label.pack(side="left", padx=5)
        
        # Create entry
        self.entry = ctk.CTkEntry(self, width=entry_width)
        self.entry.pack(side="right", fill="x", expand=True, padx=5)
        
    def get(self) -> str:
        """Get the entry value."""
        return self.entry.get()
        
    def set(self, value: str):
        """Set the entry value."""
        self.entry.delete(0, 'end')
        self.entry.insert(0, value)
        
    def clear(self):
        """Clear the entry value."""
        self.entry.delete(0, 'end')
        
    def bind(self, event: str, callback):
        """Bind an event to the entry widget."""
        self.entry.bind(event, callback)
        
    def configure_entry(self, **kwargs):
        """Configure the entry widget."""
        self.entry.configure(**kwargs)
        
    def configure_label(self, **kwargs):
        """Configure the label widget."""
        self.label.configure(**kwargs)