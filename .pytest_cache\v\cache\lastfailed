{"test/test_gui_widgets.py::TestModelDropdown::test_get_selected_model": true, "test/test_gui_widgets.py::TestModelDropdown::test_has_valid_selection": true, "test/test_gui_widgets.py::TestModelDropdown::test_initialization": true, "test/test_gui_widgets.py::TestModelDropdown::test_update_models": true, "test/test_gui_widgets.py::TestSeriesControls::test_get_series_count": true, "test/test_gui_widgets.py::TestSeriesControls::test_initialization": true, "test/test_gui_widgets.py::TestSeriesControls::test_validate_series_count": true, "test/test_gui_widgets.py::TestFormField::test_get_set_methods": true, "test/test_gui_widgets.py::TestFormField::test_initialization": true, "test/test_config_integration.py::TestConfigIntegration::test_configuration_and_logging_integration": true, "test/test_config_app.py": true, "test/test_pdf_section_processor.py": true}