"""
E3 Series COM API integration client.

This module contains the E3Client class that provides a wrapper
around the E3 Series COM API with proper resource management.
"""

import logging
import time
from typing import Optional, List, Tuple
from contextlib import contextmanager

from ..utils.resource_manager import managed_com_object, get_resource_manager, resource_monitoring

try:
    import e3series
except ImportError:
    e3series = None


class E3Client:
    """Client for E3 Series COM API integration."""

    def __init__(self, target_pid: Optional[int] = None, config_manager=None):
        """
        Initialize the E3 client.

        Args:
            target_pid: Optional PID of specific E3 instance to connect to
            config_manager: Configuration manager for loading settings
        """
        self.app = None
        self.job = None
        self.target_pid = target_pid
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self._connected = False

        # Load E3 integration configuration
        self.config = {}
        if config_manager:
            try:
                app_config = config_manager.load_app_config()
                self.config = app_config.get('e3_integration', {})
            except:
                pass

        # Set configuration values with defaults
        self.connection_timeout = self.config.get('connection_timeout', 30)
        self.retry_attempts = self.config.get('retry_attempts', 3)
        self.auto_connect = self.config.get('auto_connect', True)
        self.auto_select_instance = self.config.get('auto_select_instance', True)
        
    def connect(self) -> bool:
        """
        Establish connection to E3 Series application.
        
        Returns:
            True if connection succeeded, False otherwise
            
        Raises:
            E3ConnectionError: If connection fails and raise_on_error is True
        """
        with resource_monitoring("E3 Connection"):
            try:
                if e3series is None:
                    error_msg = "E3 Series module not available - ensure e3series package is installed"
                    self.logger.error(error_msg)
                    raise E3ConnectionError(error_msg)
                    
                if self.target_pid:
                    self.logger.info(f"Connecting to E3 Series instance with PID {self.target_pid}...")
                else:
                    self.logger.info("Connecting to E3 Series...")

                # Create application object with resource tracking
                try:
                    if self.target_pid:
                        # Use connection manager for specific PID
                        from lib.e3_connection_manager import connect_to_e3_with_pid
                        success, objects = connect_to_e3_with_pid(self.target_pid, self.logger)
                        if success:
                            self.app = objects['app']
                            self.job = objects['job']
                        else:
                            raise Exception("Failed to connect to specific E3 instance")
                    else:
                        # Use default connection method
                        self.app = e3series.Application()
                        self.job = self.app.CreateJobObject()

                    get_resource_manager().register_com_object(self.app, "E3Application")
                    get_resource_manager().register_com_object(self.job, "E3Job")
                except Exception as e:
                    error_msg = "Failed to create E3 Series Application object"
                    self.logger.error(f"{error_msg}: {e}")
                    # Clean up any created objects
                    self._cleanup_com_objects()
                    raise E3ConnectionError(error_msg, e)
                
                self._connected = True
                self.logger.info("Successfully connected to E3 Series")
                return True
                
            except E3ConnectionError:
                self._connected = False
                raise
            except Exception as e:
                error_msg = "Unexpected error during E3 Series connection"
                self.logger.error(f"{error_msg}: {e}")
                self._connected = False
                raise E3ConnectionError(error_msg, e)
            
    def disconnect(self):
        """Disconnect from E3 Series and clean up resources."""
        try:
            self.logger.info("Disconnecting from E3 Series...")
            self._cleanup_com_objects()
            self._connected = False
            self.logger.info("Successfully disconnected from E3 Series")
        except Exception as e:
            self.logger.error(f"Error during E3 disconnect: {e}")
    
    def _cleanup_com_objects(self):
        """Clean up COM objects with proper resource management."""
        try:
            # Clean up COM objects in reverse order
            if self.job is not None:
                try:
                    if hasattr(self.job, 'Release'):
                        self.job.Release()
                except Exception as e:
                    self.logger.debug(f"Error releasing job object: {e}")
                finally:
                    self.job = None
                    
            if self.app is not None:
                try:
                    if hasattr(self.app, 'Release'):
                        self.app.Release()
                except Exception as e:
                    self.logger.debug(f"Error releasing app object: {e}")
                finally:
                    self.app = None
                    
        except Exception as e:
            self.logger.warning(f"Error during COM cleanup: {e}")
            
    def is_connected(self) -> bool:
        """Check if client is connected to E3 Series."""
        return self._connected and self.app is not None

    def set_target_pid(self, pid: Optional[int]):
        """
        Set the target PID for E3 connection.

        Args:
            pid: Process ID of E3 instance to connect to, or None for default
        """
        if self.is_connected():
            self.logger.warning("Cannot change target PID while connected. Disconnect first.")
            return

        self.target_pid = pid
        self.logger.debug(f"Set target PID to: {pid}")

    def reconnect_to_instance(self, pid: int) -> bool:
        """
        Reconnect to a different E3 instance.

        Args:
            pid: Process ID of E3 instance to connect to

        Returns:
            True if reconnection succeeded, False otherwise
        """
        try:
            # Disconnect from current instance
            if self.is_connected():
                self.logger.debug("Disconnecting from current E3 instance")
                self.disconnect()

            # Add a small delay to ensure clean disconnection
            import time
            reconnect_delay = self.config.get('reconnect_delay', 0.5)
            time.sleep(reconnect_delay)

            # Set new target PID and reconnect
            self.logger.debug(f"Setting target PID to {pid} and reconnecting")
            self.set_target_pid(pid)
            success = self.connect()

            if success:
                self.logger.info(f"Successfully reconnected to E3 instance {pid}")
            else:
                self.logger.error(f"Failed to reconnect to E3 instance {pid}")

            return success

        except Exception as e:
            self.logger.error(f"Failed to reconnect to E3 instance {pid}: {e}")
            return False
            
    def read_title_block_data(self) -> Optional['TitleBlockData']:
        """
        Read title block data from the current project.
        
        Returns:
            TitleBlockData object or None if read failed
            
        Raises:
            E3ConnectionError: If not connected to E3 Series
            E3OperationError: If reading title block data fails
        """
        if not self.is_connected():
            error_msg = "Cannot read title block data - not connected to E3 Series"
            self.logger.error(error_msg)
            raise E3ConnectionError(error_msg)
            
        try:
            from ..models.project_data import TitleBlockData
            
            with resource_monitoring("Read Title Block Data"):
                self.logger.info("Reading title block data...")
                
                # Create sheet object with resource management
                try:
                    dtm_sheet = self.job.CreateSheetObject()
                except Exception as e:
                    error_msg = "Failed to create sheet object"
                    self.logger.error(f"{error_msg}: {e}")
                    raise E3OperationError(error_msg, "create_sheet_object", e)
                
                with managed_com_object(dtm_sheet, "TitleBlockSheet"):
                    # Get sheet IDs
                    try:
                        sheet_ids = self.job.GetAllSheetIds()
                    except Exception as e:
                        error_msg = "Failed to get sheet IDs"
                        self.logger.error(f"{error_msg}: {e}")
                        raise E3OperationError(error_msg, "get_sheet_ids", e)
                    
                    if not sheet_ids:
                        self.logger.warning("No sheets found in project")
                        return None
                    
                    # Initialize with empty values
                    title_block = TitleBlockData()
                    sheets_processed = 0
                    sheets_with_errors = 0
                    
                    # Iterate through sheets to find one with title block data
                    for sheet in sheet_ids:
                        if isinstance(sheet, tuple):
                            for sheet_id in sheet:
                                if sheet_id is None:
                                    continue
                                    
                                sheets_processed += 1
                                try:
                                    dtm_sheet.SetId(sheet_id)
                                    document_number = dtm_sheet.GetAttributeValue("DOCUMENT_NUMBER")
                                    
                                    # Skip sheets without document number
                                    if not document_number:
                                        self.logger.debug(f"Sheet {sheet_id} has no document number, skipping")
                                        continue
                                        
                                    # Found a sheet with data, extract all title block info
                                    self.logger.debug(f"Reading title block data from sheet {sheet_id}")
                                    title_block.document_number = document_number
                                    title_block.model = dtm_sheet.GetAttributeValue("Title1") or ""
                                    title_block.description = dtm_sheet.GetAttributeValue("Title2") or ""
                                    title_block.customer = dtm_sheet.GetAttributeValue("CUSTOMER") or ""
                                    title_block.location = dtm_sheet.GetAttributeValue("ORDER") or ""
                                    title_block.sales_order = dtm_sheet.GetAttributeValue("ORDER_NUMBER") or ""
                                    title_block.serial_number = dtm_sheet.GetAttributeValue("DRAWINGNUMBER") or ""
                                    
                                    self.logger.info(f"Successfully read title block data from sheet {sheet_id}")
                                    self.logger.debug(f"Title block data: {title_block}")
                                    return title_block
                                    
                                except Exception as exc:
                                    sheets_with_errors += 1
                                    self.logger.warning(f"Error reading from sheet ID {sheet_id}: {exc}")
                                    continue
                                    
                    # If we get here, no valid title block data was found
                    if sheets_processed == 0:
                        error_msg = "No sheets found to process"
                        self.logger.error(error_msg)
                        raise E3OperationError(error_msg, "read_title_block")
                    elif sheets_with_errors == sheets_processed:
                        error_msg = f"Failed to read from all {sheets_processed} sheets"
                        self.logger.error(error_msg)
                        raise E3OperationError(error_msg, "read_title_block")
                    else:
                        self.logger.warning(f"No title block data found in any of {sheets_processed} sheets")
                        return None
            
        except E3ConnectionError:
            raise
        except E3OperationError:
            raise
        except Exception as e:
            error_msg = "Unexpected error reading title block data"
            self.logger.error(f"{error_msg}: {e}")
            raise E3OperationError(error_msg, "read_title_block", e)
        
    def update_attributes(self, project_data):
        """
        Update project attributes with the provided data.
        
        Args:
            project_data: ProjectData containing updated values
            
        Raises:
            E3ConnectionError: If not connected to E3 Series
            E3OperationError: If updating attributes fails
            E3ValidationError: If project data validation fails
        """
        if not self.is_connected():
            error_msg = "Cannot update attributes - not connected to E3 Series"
            self.logger.error(error_msg)
            raise E3ConnectionError(error_msg)
        
        # Validate project data
        if not project_data:
            error_msg = "Project data is required for attribute updates"
            self.logger.error(error_msg)
            raise E3ValidationError(error_msg)
        
        # Validate required fields
        if not project_data.gss_parent:
            raise E3ValidationError("GSS Parent number is required", "gss_parent", project_data.gss_parent)
        if not project_data.serial_number:
            raise E3ValidationError("Serial number is required", "serial_number", project_data.serial_number)
            
        try:
            self.logger.info("Updating project attributes...")
            self.logger.debug(f"Updating with data: GSS={project_data.gss_parent}, Serial={project_data.serial_number}")
            
            # Update sheet title block data
            sheet_errors = self._update_sheet_attributes(project_data)
            
            # Update device GSS parent attributes
            device_errors = self._update_device_attributes(project_data)
            
            # Report results
            total_errors = sheet_errors + device_errors
            if total_errors > 0:
                self.logger.warning(f"Completed attribute updates with {total_errors} errors")
            else:
                self.logger.info("Successfully updated all project attributes")
            
        except E3ValidationError:
            raise
        except Exception as e:
            error_msg = "Unexpected error updating project attributes"
            self.logger.error(f"{error_msg}: {e}")
            raise E3OperationError(error_msg, "update_attributes", e)
    
    def _update_sheet_attributes(self, project_data) -> int:
        """
        Update sheet title block attributes.
        
        Returns:
            Number of errors encountered
        """
        error_count = 0
        
        try:
            dtm_sheet = self.job.CreateSheetObject()
            
            with managed_com_object(dtm_sheet, "UpdateSheetObject"):
                sheet_ids = self.job.GetAllSheetIds()
                
                if not sheet_ids:
                    self.logger.warning("No sheets found to update")
                    return 0
                
                sheets_updated = 0
                for sheet in sheet_ids:
                    if isinstance(sheet, tuple):
                        for sheet_id in sheet:
                            if sheet_id is None:
                                continue
                            try:
                                dtm_sheet.SetId(sheet_id)
                                
                                # Update all title block attributes
                                dtm_sheet.SetAttributeValue("DOCUMENT_NUMBER", project_data.gss_parent or "")
                                dtm_sheet.SetAttributeValue("Title1", project_data.model or "")
                                dtm_sheet.SetAttributeValue("Title2", project_data.title or "")
                                dtm_sheet.SetAttributeValue("CUSTOMER", project_data.customer or "")
                                dtm_sheet.SetAttributeValue("ORDER", project_data.location or "")
                                dtm_sheet.SetAttributeValue("ORDER_NUMBER", project_data.sales_order or "")
                                dtm_sheet.SetAttributeValue("DRAWINGNUMBER", project_data.serial_number or "")
                                
                                sheets_updated += 1
                                self.logger.debug(f"Updated sheet attributes for sheet ID: {sheet_id}")
                                
                            except Exception as exc:
                                error_count += 1
                                self.logger.warning(f"Error updating sheet ID {sheet_id}: {exc}")
                
                self.logger.info(f"Updated {sheets_updated} sheets with {error_count} errors")
                return error_count
            
        except Exception as e:
            error_msg = "Failed to update sheet attributes"
            self.logger.error(f"{error_msg}: {e}")
            raise E3OperationError(error_msg, "update_sheet_attributes", e)
    
    def _update_device_attributes(self, project_data) -> int:
        """
        Update device GSS parent attributes.
        
        Returns:
            Number of errors encountered
        """
        error_count = 0
        
        try:
            device = self.job.CreateDeviceObject()
            
            with managed_com_object(device, "UpdateDeviceObject"):
                device_ids = self.job.GetAllDeviceIds()
                
                # Update job-level GSS_PARENT attribute
                gss_parent_value = f"{project_data.gss_parent} {project_data.serial_number}"
                try:
                    self.job.SetAttributeValue("GSS_PARENT", gss_parent_value)
                    self.logger.debug(f"Updated job-level GSS_PARENT: {gss_parent_value}")
                except Exception as e:
                    error_count += 1
                    self.logger.warning(f"Error updating job-level GSS_PARENT: {e}")
                
                if not device_ids:
                    self.logger.info("No devices found to update")
                    return error_count
                
                # Update all devices with GSS_PARENT attribute
                devices_updated = 0
                for dev in device_ids:
                    if isinstance(dev, tuple):
                        for dev_id in dev:
                            if dev_id is None:
                                continue
                            try:
                                device.SetId(dev_id)
                                device.SetAttributeValue("GSS_PARENT", gss_parent_value)
                                devices_updated += 1
                                self.logger.debug(f"Updated device GSS_PARENT for device ID: {dev_id}")
                                
                            except Exception as exc:
                                error_count += 1
                                self.logger.warning(f"Error updating device ID {dev_id}: {exc}")
                
                self.logger.info(f"Updated {devices_updated} devices with {error_count} total errors")
                return error_count
            
        except Exception as e:
            error_msg = "Failed to update device attributes"
            self.logger.error(f"{error_msg}: {e}")
            raise E3OperationError(error_msg, "update_device_attributes", e)
    
    def get_process_id(self) -> Optional[int]:
        """
        Get the E3 Series process ID.

        Returns:
            Process ID or None if not available
        """
        if not self.is_connected():
            return self.target_pid  # Return target PID even if not connected

        try:
            # First try to get PID from the app
            pid = self.app.GetProcessProperty("ProcessID")
            if pid:
                return int(pid)

            # Fallback to target PID if available
            if self.target_pid:
                self.logger.debug(f"Using target PID as fallback: {self.target_pid}")
                return self.target_pid

            return None
        except Exception as e:
            self.logger.error(f"Error getting process ID: {e}")
            # Return target PID as fallback
            return self.target_pid
    
    def get_all_sheet_ids(self) -> List[Tuple]:
        """
        Get all sheet IDs from the current job.
        
        Returns:
            List of sheet ID tuples
        """
        if not self.is_connected():
            raise E3ConnectionError("Not connected to E3 Series")
            
        try:
            return self.job.GetAllSheetIds()
        except Exception as e:
            self.logger.error(f"Error getting sheet IDs: {e}")
            raise E3OperationError(f"Failed to get sheet IDs: {e}")
    
    def export_pdf(self, output_path: str, sheet_ids: Optional[List] = None) -> bool:
        """
        Export project sheets to PDF.

        Args:
            output_path: Path for the output PDF file
            sheet_ids: List of sheet IDs to export (None for all sheets)

        Returns:
            True if export succeeded, False otherwise
        """
        if not self.is_connected():
            raise E3ConnectionError("Not connected to E3 Series")

        try:
            if sheet_ids is None:
                sheet_ids = self.get_all_sheet_ids()

            self.logger.debug(f"Sheet IDs for PDF export: {sheet_ids} (type: {type(sheet_ids)})")

            # Handle different formats of sheet_ids returned by E3
            if isinstance(sheet_ids, (list, tuple)) and len(sheet_ids) >= 2:
                # E3 returns (count, (sheet_id1, sheet_id2, ...))
                if isinstance(sheet_ids[1], (list, tuple)):
                    actual_sheet_ids = sheet_ids[1]
                else:
                    # If sheet_ids[1] is not a tuple/list, use the whole thing
                    actual_sheet_ids = sheet_ids
            elif isinstance(sheet_ids, int):
                # Single sheet ID
                actual_sheet_ids = (sheet_ids,)
            else:
                # Use as-is
                actual_sheet_ids = sheet_ids

            self.logger.debug(f"Using sheet IDs for export: {actual_sheet_ids}")

            options = 0x4800C  # Acrobat Version 12
            result = self.job.ExportPDF(output_path, actual_sheet_ids, options, "")

            # Handle different result formats
            if isinstance(result, (list, tuple)) and len(result) > 0:
                success = result[0] == 1
            elif isinstance(result, int):
                success = result == 1
            else:
                success = bool(result)

            if success:
                self.logger.info(f"PDF successfully exported to {output_path}")
                return True
            else:
                self.logger.error(f"Failed to export PDF. Result: {result}")
                return False

        except Exception as e:
            self.logger.error(f"Error exporting PDF: {e}")
            raise E3OperationError(f"Failed to export PDF: {e}")
    
    def export_dxf_dataplates(self, output_dir: str, document_number: str, serial_number: str) -> List[str]:
        """
        Export dataplate sheets to DXF format.
        
        Args:
            output_dir: Directory for DXF output files
            document_number: Document number for filename
            serial_number: Serial number for filename
            
        Returns:
            List of exported DXF file paths
        """
        if not self.is_connected():
            raise E3ConnectionError("Not connected to E3 Series")
            
        exported_files = []
        
        try:
            dtm_sheet = self.job.CreateSheetObject()
            
            with managed_com_object(dtm_sheet, "DXFExportSheet"):
                sheet_ids = self.get_all_sheet_ids()
                
                for sheet in sheet_ids:
                    if isinstance(sheet, tuple):
                        for sheet_id in sheet:
                            if sheet_id is None:
                                continue
                            try:
                                dtm_sheet.SetId(sheet_id)
                                doc_type = dtm_sheet.GetAttributeValue("Document Type")
                                
                                if doc_type == "101 - Dataplates":
                                    import os
                                    dxf_file = os.path.join(
                                        output_dir, 
                                        f"{document_number}_{serial_number}_{sheet_id}.dxf"
                                    )
                                    
                                    result = dtm_sheet.Export("DXF", 2018, dxf_file, "2")
                                    
                                    if result == 0:
                                        self.logger.info(f"DXF successfully exported to {dxf_file}")
                                        exported_files.append(dxf_file)
                                    else:
                                        self.logger.error(f"Failed to export DXF for sheet ID {sheet_id}. Error code: {result}")
                                        
                            except Exception as exc:
                                self.logger.warning(f"Error exporting DXF for sheet ID {sheet_id}: {exc}")
                                
                return exported_files
            
        except Exception as e:
            self.logger.error(f"Error exporting DXF dataplates: {e}")
            raise E3OperationError(f"Failed to export DXF dataplates: {e}")
        
    def __enter__(self):
        """Context manager entry."""
        if self.connect():
            return self
        else:
            raise E3ConnectionError("Failed to connect to E3 Series")
            
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with proper cleanup."""
        self.disconnect()


class E3ConnectionError(Exception):
    """Exception raised when E3 Series connection fails."""
    
    def __init__(self, message: str, original_error: Exception = None):
        """
        Initialize E3 connection error.
        
        Args:
            message: Error message
            original_error: Original exception that caused this error
        """
        super().__init__(message)
        self.original_error = original_error
        self.message = message
    
    def __str__(self):
        if self.original_error:
            return f"{self.message} (Original error: {self.original_error})"
        return self.message


class E3OperationError(Exception):
    """Exception raised when E3 Series operations fail."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None):
        """
        Initialize E3 operation error.
        
        Args:
            message: Error message
            operation: Name of the operation that failed
            original_error: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.operation = operation
        self.original_error = original_error
    
    def __str__(self):
        base_msg = f"{self.message}"
        if self.operation:
            base_msg = f"Operation '{self.operation}' failed: {base_msg}"
        if self.original_error:
            base_msg += f" (Original error: {self.original_error})"
        return base_msg


class E3ValidationError(Exception):
    """Exception raised when E3 data validation fails."""
    
    def __init__(self, message: str, field: str = None, value: str = None):
        """
        Initialize E3 validation error.
        
        Args:
            message: Error message
            field: Field name that failed validation
            value: Value that failed validation
        """
        super().__init__(message)
        self.message = message
        self.field = field
        self.value = value
    
    def __str__(self):
        base_msg = self.message
        if self.field:
            base_msg = f"Field '{self.field}': {base_msg}"
        if self.value:
            base_msg += f" (Value: '{self.value}')"
        return base_msg


class E3TimeoutError(Exception):
    """Exception raised when E3 operations timeout."""
    
    def __init__(self, message: str, timeout_seconds: int = None):
        """
        Initialize E3 timeout error.
        
        Args:
            message: Error message
            timeout_seconds: Timeout duration in seconds
        """
        super().__init__(message)
        self.message = message
        self.timeout_seconds = timeout_seconds
    
    def __str__(self):
        if self.timeout_seconds:
            return f"{self.message} (Timeout: {self.timeout_seconds}s)"
        return self.message