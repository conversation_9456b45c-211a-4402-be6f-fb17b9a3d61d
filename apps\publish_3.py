"""
Main entry point for the Publish 3 application.

This module provides the minimal main entry point that initializes the service
container and starts the GUI application.
"""

import logging
import sys
import os
import traceback
import customtkinter as ctk

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from apps.publish.container import ServiceContainer
from apps.publish.gui.main_window import MainWindow
from apps.publish.config.logging_config import setup_publish_logging


class PublishApplication:
    """Main application class for the Publish 3 application."""
    
    def __init__(self):
        """Initialize the application."""
        self.logger = None
        self.container = None
        self.root = None
        self.main_window = None
        
    def setup_logging(self):
        """Set up application logging."""
        try:
            # Load logging configuration from config file
            logging_config = {}
            if self.container:
                try:
                    config_manager = self.container.get_service('ConfigurationManager')
                    if config_manager:
                        app_config = config_manager.load_app_config()
                        logging_config = app_config.get('logging', {})
                except:
                    pass

            # Use the logging configuration from the config module
            setup_publish_logging(logging_config)
            self.logger = logging.getLogger(__name__)
            self.logger.info("Logging configured successfully from configuration")
        except Exception as e:
            # Fallback to basic logging if config fails
            fallback_log_file = logging_config.get('fallback_log_file', 'publish_debug.log') if logging_config else 'publish_debug.log'
            logging.basicConfig(
                level=logging.DEBUG,
                format="%(asctime)s [%(levelname)s] %(message)s",
                handlers=[
                    logging.FileHandler(fallback_log_file),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            self.logger = logging.getLogger(__name__)
            self.logger.warning(f"Failed to use advanced logging config, using fallback: {e}")
            
    def setup_theme(self):
        """Set up the application theme."""
        try:
            # Get theme configuration
            config_manager = self.container.get_service('ConfigurationManager') if self.container else None
            config = config_manager.load_app_config() if config_manager else {}
            theme_config = config.get('gui', {}).get('theme', {})

            # Apply appearance mode from config
            appearance_mode = theme_config.get('appearance_mode', 'dark')
            ctk.set_appearance_mode(appearance_mode)

            # Try to load custom theme from config
            custom_theme_path = theme_config.get('custom_theme_path', 'resources/themes/red.json')
            theme_path = os.path.join(parent_dir, custom_theme_path)

            if os.path.exists(theme_path):
                ctk.set_default_color_theme(theme_path)
                self.logger.info(f"Applied custom theme: {theme_path}")
            else:
                # Use configured color theme or fallback
                color_theme = theme_config.get('color_theme', 'red')
                ctk.set_default_color_theme(color_theme)
                self.logger.info(f"Applied built-in theme: {color_theme}")

        except Exception as e:
            self.logger.warning(f"Failed to apply theme: {e}")
            # Use fallback theme from config or default
            fallback_theme = theme_config.get('fallback_theme', 'blue') if 'theme_config' in locals() else 'blue'
            ctk.set_default_color_theme(fallback_theme)
            
    def initialize_container(self):
        """Initialize the service container."""
        try:
            self.logger.info("Initializing service container")
            self.container = ServiceContainer()
            self.logger.info("Service container initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize service container: {e}")
            raise
            
    def create_gui(self):
        """Create the main GUI window."""
        try:
            self.logger.info("Creating main GUI window")

            # Get window configuration
            config_manager = self.container.get_service('ConfigurationManager') if self.container else None
            config = config_manager.load_app_config() if config_manager else {}
            window_config = config.get('gui', {}).get('window', {})

            # Create window with configured settings
            self.root = ctk.CTk()

            # Set window title from config
            title = window_config.get('title', 'Publish Project')
            self.root.title(title)

            # Set window geometry from config
            geometry = window_config.get('geometry', '600x500')
            self.root.geometry(geometry)

            # Set minimum size from config
            min_width = window_config.get('min_width', 500)
            min_height = window_config.get('min_height', 400)
            self.root.minsize(min_width, min_height)

            # Create the main window with dependency injection
            self.main_window = MainWindow(self.root, self.container)

            self.logger.info(f"Main GUI window created successfully: {title} ({geometry})")

        except Exception as e:
            self.logger.error(f"Failed to create GUI: {e}")
            raise
            
    def run(self):
        """Run the application."""
        try:
            self.logger.info("Starting Publish 3 application")
            
            # Start the GUI main loop
            if self.root:
                self.logger.info("Starting GUI main loop")
                self.root.mainloop()
            else:
                raise RuntimeError("GUI not initialized")
                
        except Exception as e:
            self.logger.error(f"Error in application main loop: {e}")
            raise
        finally:
            self.cleanup()
            
    def cleanup(self):
        """Clean up application resources."""
        try:
            self.logger.info("Cleaning up application resources")
            
            if self.container:
                self.container.dispose()
                
            self.logger.info("Application cleanup completed")
            
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")
            
    def handle_frozen_environment(self):
        """Handle PyInstaller frozen environment setup."""
        is_frozen = getattr(sys, 'frozen', False)
        
        if is_frozen:
            try:
                # Add the _MEIPASS directory to the PATH environment variable
                os.environ['PATH'] = sys._MEIPASS + os.pathsep + os.environ['PATH']
                
                # Change working directory to the executable directory
                os.chdir(os.path.dirname(sys.executable))
                
                # Set up logging to a file in the user's temp directory
                log_file = os.path.join(os.path.expanduser('~'), 'publish_3.log')
                logging.basicConfig(
                    level=logging.DEBUG,
                    format="%(asctime)s [%(levelname)s] %(message)s",
                    handlers=[
                        logging.FileHandler(log_file),
                        logging.StreamHandler(sys.stdout)
                    ]
                )
                
                if self.logger:
                    self.logger.info(f"Running from frozen environment: {sys.executable}")
                    
            except Exception as e:
                print(f"Error setting up frozen environment: {str(e)}")
                
    def start(self):
        """Start the application with full initialization."""
        try:
            # Handle frozen environment first
            self.handle_frozen_environment()
            
            # Set up logging
            self.setup_logging()
            
            # Set up theme
            self.setup_theme()
            
            # Initialize service container
            self.initialize_container()
            
            # Create GUI
            self.create_gui()
            
            # Run the application
            self.run()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Fatal error during application startup: {e}")
                self.logger.error(traceback.format_exc())
            else:
                print(f"Fatal error during application startup: {e}")
                print(traceback.format_exc())
            sys.exit(1)


def main():
    """Main entry point function."""
    app = PublishApplication()
    app.start()


if __name__ == "__main__":
    main()