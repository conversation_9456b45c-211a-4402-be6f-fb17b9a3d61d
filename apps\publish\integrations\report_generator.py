"""
Report Generator integration client.

This module contains the ReportGeneratorClient class for running
BOM reports and updating characteristics through the Report Generator.
"""

import logging
import subprocess
import os
import datetime
import psutil
from typing import List, Optional, Dict, Any
from pathlib import Path

from ..utils.resource_manager import resource_monitoring


class ReportGeneratorClient:
    """Client for Report Generator integration."""
    
    def __init__(self, report_generator_path: str = None, config_path: str = None):
        """
        Initialize the Report Generator client.
        
        Args:
            report_generator_path: Path to creatorbatch.exe (optional)
            config_path: Path to configuration file (optional)
        """
        self.logger = logging.getLogger(__name__)
        
        # Set default paths
        self.report_generator_path = (
            report_generator_path or 
            r"C:\Program Files\Zuken\E3.ReportGenerator\creatorbatch.exe"
        )
        self.config_path = (
            config_path or 
            r"C:\ProgramData\Zuken\E3.ReportGenerator\Configuration\E3.ReportGenerator.ini"
        )
        
        # Validate paths on initialization
        self._validate_paths()
        
    def _validate_paths(self):
        """Validate that required paths exist."""
        if not os.path.exists(self.report_generator_path):
            self.logger.warning(f"Report Generator executable not found: {self.report_generator_path}")
            
        if not os.path.exists(self.config_path):
            self.logger.warning(f"Report Generator configuration not found: {self.config_path}")
        
    def run_bom_report(self, report_name: str, export_format: str, 
                      e3_pid: int, export_path: str = None, timeout: int = 300) -> bool:
        """
        Run a BOM report using Report Generator.
        
        Args:
            report_name: Name of the report to run
            export_format: Export format (e.g., "ExcelExport", "e3.sheet")
            e3_pid: E3 Series process ID
            export_path: Path for exported file (optional)
            timeout: Timeout in seconds (default: 300)
            
        Returns:
            True if report generation succeeded, False otherwise
            
        Raises:
            ReportGeneratorError: If report generation fails
        """
        if not report_name:
            raise ReportGeneratorValidationError("Report name is required", "report_name", report_name)
        if not export_format:
            raise ReportGeneratorValidationError("Export format is required", "export_format", export_format)
        if not e3_pid or e3_pid <= 0:
            raise ReportGeneratorValidationError("Valid E3 process ID is required", "e3_pid", str(e3_pid))
            
        with resource_monitoring(f"Report Generation: {report_name}"):
            try:
                # Build command
                command = [
                    self.report_generator_path,
                    "-c", self.config_path,
                    "--reportname", report_name,
                    "--exportformat", export_format,
                    "--e3pid", str(e3_pid),
                ]
                
                if export_path:
                    # Ensure export directory exists
                    export_dir = os.path.dirname(export_path)
                    if export_dir and not os.path.exists(export_dir):
                        try:
                            os.makedirs(export_dir, exist_ok=True)
                            self.logger.info(f"Created export directory: {export_dir}")
                        except Exception as e:
                            self.logger.error(f"Failed to create export directory {export_dir}: {e}")
                            raise ReportGeneratorError(f"Failed to create export directory: {e}", "create_directory", e)
                    
                    command.extend(["--exportpath", export_path])
                    
                self.logger.info(f"Running report: {report_name} (Format: {export_format}, PID: {e3_pid})")
                self.logger.debug(f"Command: {' '.join(command)}")
                
                # Run the command with timeout and resource tracking
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                
                # Check result
                if result.returncode == 0:
                    self.logger.info(f"Report completed successfully: {report_name}")
                    if result.stdout:
                        self.logger.debug(f"Command output: {result.stdout}")
                    
                    # Verify export file exists if path was specified
                    if export_path and not os.path.exists(export_path):
                        self.logger.warning(f"Export file not found after successful report: {export_path}")
                        
                    return True
                else:
                    error_msg = f"Report failed with return code {result.returncode}: {report_name}"
                    self.logger.error(error_msg)
                    if result.stdout:
                        self.logger.error(f"Command output: {result.stdout}")
                    if result.stderr:
                        self.logger.error(f"Command errors: {result.stderr}")
                    
                    raise ReportGeneratorError(
                        error_msg, 
                        "run_report", 
                        None, 
                        result.returncode, 
                        result.stdout, 
                        result.stderr
                    )
                    
            except subprocess.TimeoutExpired as e:
                error_msg = f"Report timed out after {timeout} seconds: {report_name}"
                self.logger.error(error_msg)
                raise ReportGeneratorTimeoutError(error_msg, timeout)
            except ReportGeneratorError:
                raise
            except Exception as e:
                error_msg = f"Unexpected error running report {report_name}"
                self.logger.error(f"{error_msg}: {e}")
                raise ReportGeneratorError(error_msg, "run_report", e)
            
    def update_bom_reports(self, e3_pid: int, report_names: List[str] = None,
                          update_characteristics: bool = True) -> Dict[str, bool]:
        """
        Update multiple BOM reports in the project.

        Args:
            e3_pid: E3 Series process ID
            report_names: List of report names to update (default: standard reports)
            update_characteristics: Whether to update drawing characteristics

        Returns:
            Dictionary mapping report names to success status

        Raises:
            ReportGeneratorError: If critical errors occur
        """
        if report_names is None:
            report_names = ["PHOENIX BOM", "Simplified BOM"]
            
        if not report_names:
            self.logger.warning("No report names provided for update")
            return {}
            
        self.logger.info(f"Updating {len(report_names)} BOM reports")
        results = {}

        # Update each report
        for report_name in report_names:
            try:
                success = self.run_bom_report(report_name, "E3Export", e3_pid)
                results[report_name] = success

                if success:
                    self.logger.info(f"Successfully updated report: {report_name}")
                else:
                    self.logger.error(f"Failed to update report: {report_name}")

            except Exception as e:
                self.logger.error(f"Error updating report {report_name}: {e}")
                results[report_name] = False
        
        # Update drawing characteristics if requested
        if update_characteristics:
            try:
                self._update_drawing_characteristics(e3_pid)
            except Exception as e:
                self.logger.error(f"Failed to update drawing characteristics: {e}")
        
        # Report summary
        successful_reports = sum(1 for success in results.values() if success)
        self.logger.info(f"BOM update completed: {successful_reports}/{len(report_names)} reports successful")
        
        return results
    
    def _update_drawing_characteristics(self, e3_pid: int):
        """
        Update drawing characteristics for BOM sheets.
        
        This method would typically require E3 integration to update sheet characteristics.
        For now, it logs the operation.
        """
        self.logger.info("Updating drawing characteristics to 'Panel Configuration'")
        # Note: This functionality requires E3Client integration
        # Implementation would be moved to E3Client or coordinated between both clients
        
    def export_gss_bom(self, e3_pid: int, document_number: str, 
                      serial_number: str, export_base_path: str = None) -> Optional[str]:
        """
        Export GSS BOM to Excel format.
        
        Args:
            e3_pid: E3 Series process ID
            document_number: Document number for filename
            serial_number: Serial number for filename
            export_base_path: Base path for export (default: T:\\ENG\\Common\\GSS ELEC CAD BOMS\\)
            
        Returns:
            Path to exported file if successful, None otherwise
            
        Raises:
            ReportGeneratorError: If export fails
        """
        if not document_number:
            raise ReportGeneratorValidationError("Document number is required", "document_number", document_number)
        if not serial_number:
            raise ReportGeneratorValidationError("Serial number is required", "serial_number", serial_number)
            
        # Set default export path
        if export_base_path is None:
            export_base_path = r"T:\ENG\Common\GSS ELEC CAD BOMS"
            
        # Generate timestamped filename with conflict resolution
        now = datetime.datetime.now()
        base_filename = f"{document_number} {serial_number} {now:%Y-%m-%d %H.%M}"
        filename = f"{base_filename}.xlsx"
        export_path = os.path.join(export_base_path, filename)

        # Check for file conflicts and resolve them
        counter = 1
        while os.path.exists(export_path):
            try:
                # Try to open the file to see if it's in use
                with open(export_path, 'r+b'):
                    pass
                # If we can open it, it's not in use, so we can overwrite
                break
            except (IOError, OSError):
                # File is in use, create a new filename
                filename = f"{base_filename} ({counter}).xlsx"
                export_path = os.path.join(export_base_path, filename)
                counter += 1
                if counter > 10:  # Prevent infinite loop
                    raise ReportGeneratorError(f"Too many file conflicts for {base_filename}", "file_conflict")
        
        self.logger.info(f"Exporting GSS BOM to: {export_path}")
        
        try:
            success = self.run_bom_report("GSS BOM", "ExcelExport", e3_pid, export_path)
            
            if success:
                # Verify file was created
                if os.path.exists(export_path):
                    file_size = os.path.getsize(export_path)
                    self.logger.info(f"GSS BOM exported successfully: {export_path} ({file_size} bytes)")
                    return export_path
                else:
                    self.logger.error(f"Export reported success but file not found: {export_path}")
                    return None
            else:
                self.logger.error("GSS BOM export failed")
                return None
                
        except Exception as e:
            self.logger.error(f"Error exporting GSS BOM: {e}")
            raise
    
    def is_available(self) -> bool:
        """
        Check if Report Generator is available.
        
        Returns:
            True if Report Generator executable and config exist
        """
        return (
            os.path.exists(self.report_generator_path) and 
            os.path.exists(self.config_path)
        )
    
    def get_version_info(self) -> Optional[str]:
        """
        Get Report Generator version information.
        
        Returns:
            Version string if available, None otherwise
        """
        try:
            # Try to get version info (this may not work with all versions)
            result = subprocess.run(
                [self.report_generator_path, "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout:
                return result.stdout.strip()
            else:
                # Fallback to file properties
                if os.path.exists(self.report_generator_path):
                    stat = os.stat(self.report_generator_path)
                    return f"File modified: {datetime.datetime.fromtimestamp(stat.st_mtime)}"
                    
        except Exception as e:
            self.logger.debug(f"Could not get version info: {e}")
            
        return None


class ReportGeneratorError(Exception):
    """Base exception for Report Generator operations."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None,
                 return_code: int = None, stdout: str = None, stderr: str = None):
        """
        Initialize Report Generator error.
        
        Args:
            message: Error message
            operation: Name of the operation that failed
            original_error: Original exception that caused this error
            return_code: Process return code
            stdout: Process stdout
            stderr: Process stderr
        """
        super().__init__(message)
        self.message = message
        self.operation = operation
        self.original_error = original_error
        self.return_code = return_code
        self.stdout = stdout
        self.stderr = stderr
    
    def __str__(self):
        base_msg = self.message
        if self.operation:
            base_msg = f"Operation '{self.operation}' failed: {base_msg}"
        if self.return_code is not None:
            base_msg += f" (Return code: {self.return_code})"
        if self.original_error:
            base_msg += f" (Original error: {self.original_error})"
        return base_msg


class ReportGeneratorValidationError(ReportGeneratorError):
    """Exception raised when Report Generator input validation fails."""
    
    def __init__(self, message: str, field: str = None, value: str = None):
        """
        Initialize Report Generator validation error.
        
        Args:
            message: Error message
            field: Field name that failed validation
            value: Value that failed validation
        """
        super().__init__(message)
        self.field = field
        self.value = value
    
    def __str__(self):
        base_msg = self.message
        if self.field:
            base_msg = f"Field '{self.field}': {base_msg}"
        if self.value:
            base_msg += f" (Value: '{self.value}')"
        return base_msg


class ReportGeneratorTimeoutError(ReportGeneratorError):
    """Exception raised when Report Generator operations timeout."""
    
    def __init__(self, message: str, timeout_seconds: int = None):
        """
        Initialize Report Generator timeout error.
        
        Args:
            message: Error message
            timeout_seconds: Timeout duration in seconds
        """
        super().__init__(message)
        self.timeout_seconds = timeout_seconds
    
    def __str__(self):
        if self.timeout_seconds:
            return f"{self.message} (Timeout: {self.timeout_seconds}s)"
        return self.message