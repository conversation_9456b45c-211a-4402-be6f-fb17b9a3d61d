"""
Dependency injection container for the publish application.

This module provides a service container that manages service dependencies
and lifecycle, enabling easy testing with mock services.
"""

import logging
from typing import Dict, Any, TypeVar, Type, Optional, Callable
from functools import lru_cache

from .config.configuration_manager import ConfigurationManager
from .integrations.e3_client import E3Client
from .integrations.report_generator import ReportGeneratorClient
from .services.model_service import ModelService
from .services.export_service import ExportService
from .services.manual_service import ManualService
from .services.publish_service import PublishService

T = TypeVar('T')


class ServiceContainer:
    """
    Service container for dependency injection and service lifecycle management.
    
    This container manages service dependencies and provides a centralized way
    to configure and retrieve service instances.
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize the service container.
        
        Args:
            config_dir: Optional path to configuration directory
        """
        self.logger = logging.getLogger(__name__)
        self._services: Dict[Type, Any] = {}
        self._factories: Dict[Type, Callable] = {}
        self._singletons: Dict[Type, Any] = {}
        self.config_dir = config_dir
        
        # Configure default service factories
        self._configure_services()
        
    def _configure_services(self):
        """Configure service dependencies and factories."""
        self.logger.debug("Configuring service dependencies")
        
        # Configuration Manager (singleton)
        self.register_singleton(ConfigurationManager, self._create_configuration_manager)
        
        # Integration services (singletons for resource management)
        self.register_singleton(E3Client, self._create_e3_client)
        self.register_singleton(ReportGeneratorClient, self._create_report_generator_client)
        
        # Business services (singletons)
        self.register_singleton(ModelService, self._create_model_service)
        self.register_singleton(ExportService, self._create_export_service)
        self.register_singleton(ManualService, self._create_manual_service)
        self.register_singleton(PublishService, self._create_publish_service)
        
    def register_singleton(self, service_type: Type[T], factory: Callable[[], T]):
        """
        Register a singleton service with its factory function.
        
        Args:
            service_type: Type of the service to register
            factory: Factory function that creates the service instance
        """
        self._factories[service_type] = factory
        self.logger.debug(f"Registered singleton service: {service_type.__name__}")
        
    def register_transient(self, service_type: Type[T], factory: Callable[[], T]):
        """
        Register a transient service with its factory function.
        
        Args:
            service_type: Type of the service to register
            factory: Factory function that creates the service instance
        """
        self._factories[service_type] = factory
        self.logger.debug(f"Registered transient service: {service_type.__name__}")
        
    def register_instance(self, service_type: Type[T], instance: T):
        """
        Register a specific instance for a service type.
        
        Args:
            service_type: Type of the service
            instance: Pre-created instance to use
        """
        self._services[service_type] = instance
        self.logger.debug(f"Registered service instance: {service_type.__name__}")
        
    def get_service(self, service_type: Type[T]) -> T:
        """
        Get a service instance of the specified type.
        
        Args:
            service_type: Type of service to retrieve
            
        Returns:
            Service instance
            
        Raises:
            ServiceNotRegisteredError: If service type is not registered
            ServiceCreationError: If service creation fails
        """
        # Check if we have a pre-registered instance
        if service_type in self._services:
            return self._services[service_type]
            
        # Check if we have a singleton instance
        if service_type in self._singletons:
            return self._singletons[service_type]
            
        # Check if we have a factory
        if service_type in self._factories:
            try:
                instance = self._factories[service_type]()
                
                # Store singleton instances
                if service_type in [ConfigurationManager, E3Client, ReportGeneratorClient, 
                                  ModelService, ExportService, ManualService, PublishService]:
                    self._singletons[service_type] = instance
                    
                return instance
                
            except Exception as e:
                error_msg = f"Failed to create service {service_type.__name__}: {e}"
                self.logger.error(error_msg)
                raise ServiceCreationError(error_msg, service_type, e)
        
        raise ServiceNotRegisteredError(f"Service {service_type.__name__} is not registered")
        
    def has_service(self, service_type: Type[T]) -> bool:
        """
        Check if a service type is registered.
        
        Args:
            service_type: Type of service to check
            
        Returns:
            True if service is registered, False otherwise
        """
        return (service_type in self._services or 
                service_type in self._singletons or 
                service_type in self._factories)
        
    def clear_singletons(self):
        """Clear all singleton instances (useful for testing)."""
        self.logger.debug("Clearing singleton instances")
        self._singletons.clear()
        
    def dispose(self):
        """Dispose of all services and clean up resources."""
        self.logger.info("Disposing service container")
        
        # Dispose of services that implement context manager protocol
        for service in list(self._singletons.values()) + list(self._services.values()):
            if hasattr(service, '__exit__'):
                try:
                    service.__exit__(None, None, None)
                except Exception as e:
                    self.logger.warning(f"Error disposing service {type(service).__name__}: {e}")
                    
        self._singletons.clear()
        self._services.clear()
        
    # Service factory methods
    
    def _create_configuration_manager(self) -> ConfigurationManager:
        """Create ConfigurationManager instance."""
        self.logger.debug("Creating ConfigurationManager")
        return ConfigurationManager(config_dir=self.config_dir)
        
    def _create_e3_client(self) -> E3Client:
        """Create E3Client instance."""
        self.logger.debug("Creating E3Client")
        return E3Client()

    def create_e3_client_with_pid(self, pid: int) -> E3Client:
        """
        Create E3Client instance for specific PID.

        Args:
            pid: Process ID of E3 instance to connect to

        Returns:
            E3Client configured for the specified PID
        """
        self.logger.debug(f"Creating E3Client for PID {pid}")
        return E3Client(target_pid=pid)
        
    def _create_report_generator_client(self) -> ReportGeneratorClient:
        """Create ReportGeneratorClient instance."""
        self.logger.debug("Creating ReportGeneratorClient")
        
        # Get configuration for report generator paths
        try:
            config_manager = self.get_service(ConfigurationManager)
            report_generator_path = config_manager.get_app_setting('report_generator_path')
            config_path = config_manager.get_app_setting('report_generator_config_path')
            
            return ReportGeneratorClient(
                report_generator_path=report_generator_path,
                config_path=config_path
            )
        except Exception as e:
            self.logger.warning(f"Failed to get report generator configuration: {e}")
            # Fall back to default paths
            return ReportGeneratorClient()
            
    def _create_model_service(self) -> ModelService:
        """Create ModelService instance."""
        self.logger.debug("Creating ModelService")
        
        try:
            config_manager = self.get_service(ConfigurationManager)
            models_config = config_manager.load_models_config()
            return ModelService(models_config=models_config)
        except Exception as e:
            self.logger.warning(f"Failed to load models configuration: {e}")
            # Fall back to empty configuration
            return ModelService(models_config={})
            
    def _create_export_service(self) -> ExportService:
        """Create ExportService instance."""
        self.logger.debug("Creating ExportService")
        
        try:
            e3_client = self.get_service(E3Client)
            return ExportService(e3_client=e3_client)
        except Exception as e:
            self.logger.warning(f"Failed to get E3Client for ExportService: {e}")
            # Fall back to no E3 client
            return ExportService(e3_client=None)
            
    def _create_manual_service(self) -> ManualService:
        """Create ManualService instance."""
        self.logger.debug("Creating ManualService")
        
        try:
            config_manager = self.get_service(ConfigurationManager)
            models_config = config_manager.load_models_config()
            return ManualService(models_config=models_config)
        except Exception as e:
            self.logger.warning(f"Failed to load models configuration for ManualService: {e}")
            # Fall back to empty configuration
            return ManualService(models_config={})
            
    def _create_publish_service(self) -> PublishService:
        """Create PublishService instance."""
        self.logger.debug("Creating PublishService")
        
        try:
            e3_client = self.get_service(E3Client)
            export_service = self.get_service(ExportService)
            manual_service = self.get_service(ManualService)
            report_generator = self.get_service(ReportGeneratorClient)
            
            return PublishService(
                e3_client=e3_client,
                export_service=export_service,
                manual_service=manual_service,
                report_generator=report_generator
            )
        except Exception as e:
            self.logger.warning(f"Failed to create dependencies for PublishService: {e}")
            # Fall back to minimal service
            return PublishService()


class ServiceNotRegisteredError(Exception):
    """Exception raised when a requested service is not registered."""
    
    def __init__(self, message: str):
        """
        Initialize service not registered error.
        
        Args:
            message: Error message
        """
        super().__init__(message)
        self.message = message


class ServiceCreationError(Exception):
    """Exception raised when service creation fails."""
    
    def __init__(self, message: str, service_type: Type = None, original_error: Exception = None):
        """
        Initialize service creation error.
        
        Args:
            message: Error message
            service_type: Type of service that failed to create
            original_error: Original exception that caused the failure
        """
        super().__init__(message)
        self.message = message
        self.service_type = service_type
        self.original_error = original_error